#!/usr/bin/env python3
"""
Script to merge results from multiple part files into a single output file.
"""

import json
import argparse
import os
import glob
from typing import List, Dict, Any
from tqdm import tqdm


def merge_results(input_pattern: str, output_path: str):
    """Merge multiple JSONL files matching the pattern into a single file."""
    
    # Find all matching files
    input_files = sorted(glob.glob(input_pattern))
    
    if not input_files:
        print(f"No files found matching pattern: {input_pattern}")
        return
    
    print(f"Found {len(input_files)} files to merge:")
    for f in input_files:
        print(f"  - {f}")
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    total_lines = 0
    
    with open(output_path, 'w', encoding='utf-8') as outfile:
        for input_file in tqdm(input_files, desc="Merging files"):
            if not os.path.exists(input_file):
                print(f"Warning: File {input_file} does not exist, skipping...")
                continue
            
            file_lines = 0
            try:
                with open(input_file, 'r', encoding='utf-8') as infile:
                    for line in infile:
                        line = line.strip()
                        if line:  # Skip empty lines
                            # Validate JSON
                            try:
                                json.loads(line)
                                outfile.write(line + '\n')
                                file_lines += 1
                                total_lines += 1
                            except json.JSONDecodeError:
                                print(f"Warning: Invalid JSON in {input_file}, skipping line")
                                continue
                
                print(f"Merged {file_lines} lines from {input_file}")
                
            except Exception as e:
                print(f"Error processing {input_file}: {e}")
                continue
    
    print(f"Merge complete! Total lines: {total_lines}")
    print(f"Output saved to: {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Merge multiple JSONL result files")
    parser.add_argument("--input_pattern", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_part_*.jsonl",
                       help="Pattern to match input files (use wildcards)")
    parser.add_argument("--output_path", type=str,
                       default="data/swe_rl_train/rl_all_raw_multiple_samples_merged.jsonl",
                       help="Path for merged output file")
    
    args = parser.parse_args()
    
    merge_results(args.input_pattern, args.output_path)


if __name__ == "__main__":
    main()
