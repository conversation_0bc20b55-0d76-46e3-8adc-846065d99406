{"total_instances": 50960, "qualified_instances": 2246, "unqualified_instances": 48714, "total_samples": 407808, "qualified_samples": 4031, "qualification_rate": 4.4073783359497645, "performance_threshold": 0.5, "score_statistics": {"mean_score": 0.24360989027496555, "median_score": 0.20534797594602522, "max_score": 1.0, "min_score": 0.0, "total_valid_scores": 46664}, "qualified_instances_list": ["Qiskit__qiskit-760", "freedomofpress__securedrop-3672", "scrapy__scrapy.35212ec5.lm_rewrite__b3fyla26", "mlcommons__GaNDLF-729", "pallets__werkzeug-2721", "repoze__repoze.sendmail-38", "tinos<PERSON>zer__PyBaMM-33", "ipython__ipython-3233", "GenericMappingTools__pygmt-1258", "jazzband__django-oauth-toolkit-1147", "pre-commit__pre-commit-81", "opsdroid__opsdroid-523", "liqd__a4-meinberlin-1813", "conda__conda-7178", "digitalfabrik__integreat-cms-285", "dmlc__dgl-6376", "encode__starlette.db5063c2.lm_rewrite__zt3cux82", "conan-io__conan-6058", "huggingface__accelerate-43", "microsoft__botbuilder-python-741", "CybOXProject__python-cybox-265", "cookiecutter__cookiecutter-62", "predicador37__pyjstat-31", "sunpy__sunpy-3356", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__7vfdpjhq", "googleapis__google-cloud-python-3756", "PyGith<PERSON>__PyGithub-1469", "mathesar-foundation__mathesar-3499", "twisted__ldaptor-106", "PrefectHQ__prefect-8802", "mantl__mantl-1652", "getredash__redash-998", "dask__dask-3453", "scrapy__scrapy.35212ec5.lm_rewrite__sdwcoseh", "conan-io__conan-2708", "Qiskit__qiskit-10466", "electricitymaps__electricitymaps-contrib-1155", "oauthlib__oauthlib.1fd52536.lm_rewrite__zrobu0uy", "rabitt__pysox-103", "ibis-project__ibis-8471", "pennersr__django-allauth-3107", "mozillazg__python-pinyin.e42dede5.lm_rewrite__wdpprvnh", "open-mmlab__mmcv-474", "modin-project__modin-4215", "spyder-ide__spyder-3765", "translate__pootle-5160", "lepture__mistune.bf54ef67.lm_rewrite__zpm27z53", "oasis-open__cti-python-stix2-542", "sqlfluff__sqlfluff.50a1c4b6.lm_rewrite__cupu2wpp", "spacetelescope__jwst-8336", "cornellius-gp__gpytorch-2285", "lincolnloop__python-qrcode.456b01d4.lm_rewrite__ppea2e0c", "fossasia__open-event-server-4804", "evennia__evennia-2563", "praw-dev__prawcore-90", "python-discord__bot-448", "cool-RR__PySnooper.57472b46.lm_rewrite__5rvf0awz", "projectmesa__mesa-2125", "ansible__ansible-16349", "keras-team__keras-19618", "redis__redis-py-1641", "Parquery__icontract-115", "ansible-collections__community.general-970", "facebookresearch__hydra.0f03eb60.lm_rewrite__mdud7ca3", "spack__spack-39146", "oauthlib__oauthlib.1fd52536.lm_rewrite__xcsf68u5", "dask__dask.5f61e423.lm_rewrite__7vxryfwk", "sqlfluff__sqlfluff-3066", "localstack__localstack-1964", "keleshev__schema.24a30457.lm_rewrite__n1lxtqoc", "aws-cloudformation__cfn-lint-1227", "contentful__contentful.py-49", "mitmproxy__mitmproxy-2781", "l-vo__photos-picker-49", "pierrea<PERSON>__picard-45", "seatgeek__thefuzz.8a05a3ee.func_basic__3eo2l5au", "huggingface__text-generation-inference-1947", "sopel-irc__sopel-1677", "freqtrade__freqtrade-1656", "SciTools__cartopy-548", "Cog-Creators__Red-DiscordBot.33e0eac7.lm_rewrite__lgb4ejah", "ansible__ansible-36271", "lepture__mistune.bf54ef67.pr_384", "mido__mido.a0158ff9.combine_file__lhvq9d26", "dask__dask.5f61e423.lm_rewrite__t2706o6g", "AnalogJ__lexicon-1356", "kserve__kserve-362", "cornellius-gp__gpytorch-761", "stanfordnlp__dspy.651a4c71.func_pm_remove_cond__pk8ijm55", "python-poetry__poetry-7671", "eliben__pyc<PERSON>ser-346", "Project-MONAI__MONAI.a09c1f08.lm_rewrite__qhdydweb", "huggingface__transformers-8435", "iterative__dvc-8802", "canonical__operator-195", "streamlit__streamlit-3975", "pre-commit__pre-commit-1043", "DataDog__integrations-core-2353", "sqlfluff__sqlfluff.50a1c4b6.lm_rewrite__uqe9en51", "Project-MONAI__MONAI-2461", "translate__pootle-4277", "tobymao__sqlglot-790", "life4__textdistance.c3aca916.lm_rewrite__v32hozkt", "Parsl__parsl-389", "spack__spack-8276", "Viatorus__quom-43", "pallets__jinja.ada0a9a6.lm_rewrite__nmeyg2pw", "stanfordnlp__dspy.651a4c71.lm_rewrite__vo753ybs", "huggingface__trl-660", "cookiecutter__cookiecutter.b4451231.lm_rewrite__f7fj0uzl", "getmoto__moto-6208", "jd<PERSON><PERSON>son__OpenAlchemy-128", "PrefectHQ__prefect-506", "ivy-llc__ivy-27819", "duckinator__emanate-152", "getnikola__nikola.0f4c230e.lm_rewrite__222ahj48", "XKNX__xknx-1663", "dask__dask.5f61e423.lm_rewrite__c4wun310", "marshmallow-code__webargs.dbde72fe.combine_file__j8qdfmw9", "facebookresearch__fvcore.a491d5b9.lm_rewrite__yt1eybxk", "aio-libs__aiohttp-2657", "deepchecks__deepchecks-1306", "dbt-labs__dbt-core-3165", "spack__spack-41724", "oauthlib__oauthlib.1fd52536.lm_rewrite__367hem7k", "pantsbuild__pants-18258", "deepset-ai__haystack-4273", "stephenmcd__mezzanine-780", "agronholm__typeguard.b6a7e438.combine_file__5py5l9eu", "ivy-llc__ivy-28091", "reframe-hpc__reframe-3339", "scikit-image__scikit-image-4589", "conan-io__conan-center-index-10656", "django-import-export__django-import-export-1853", "pytest-dev__apipkg-19", "saleor__saleor-5004", "tweepy__tweepy.91a41c6e.func_pm_remove_assign__4620uox6", "facebookresearch__hydra.0f03eb60.lm_rewrite__kuv41wav", "sunpy__sunpy-4430", "fossasia__open-event-server-5238", "rucio__rucio-5952", "google__flax-3344", "spotify__luigi-2323", "gitpython-developers__GitPython-1440", "django-cms__django-cms-3023", "spack__spack-19590", "getmoto__moto.694ce1f4.lm_rewrite__dynbbij6", "Qiskit__qiskit-1371", "bids-standard__pybids-589", "ESMCI__cime-3925", "crytic__slither-1971", "<PERSON><PERSON><PERSON>__openapi3-75", "pallets__click-1850", "nonebot__nonebot2-711", "dbt-labs__dbt-core-2841", "pulp__pulpcore-3286", "pypa__setuptools_scm-505", "cupy__cupy-1138", "pygments__pygments.27649ebb.lm_rewrite__k9xfw014", "HypothesisWorks__hypothesis-3847", "pudo__dataset.5c2dc8d3.func_pm_op_change_const__06dgb4ak", "Qiskit__qiskit-8998", "matthewwithanm__python-markdownify.6258f5c3.lm_rewrite__utjp40m8", "freqtrade__freqtrade-4144", "mozilla__bleach.73871d76.func_pm_remove_cond__kcyz9gdr", "lepture__mistune.bf54ef67.lm_rewrite__zkv6la9f", "pypa__pip-10459", "lightly-ai__lightly-164", "bloomberg__attrs-strict-38", "python-discord__bot-1452", "django-extensions__django-extensions-470", "joke2k__faker-1046", "dask__dask.5f61e423.lm_rewrite__wju8l2n8", "cantools__cantools.0c6a7871.lm_rewrite__g368ni0a", "django-json-api__django-rest-framework-json-api-87", "ultrabug__py3status-1374", "alltheplaces__alltheplaces-2338", "cloud-custodian__cloud-custodian-4103", "Pylons__colander-324", "getmoto__moto-1400", "networkx__networkx-1045", "astropy__reproject-334", "scanny__python-pptx.278b47b1.lm_rewrite__hsyj1njt", "joke2k__faker.8b401a7d.lm_rewrite__szpd1hqw", "lepture__mistune.bf54ef67.lm_rewrite__tgllulgm", "geopandas__geopandas-1963", "freedomofpress__securedrop-188", "python-jsonschema__jsonschema.93e0caa5.pr_1224", "ross__requests-futures-69", "encode__starlette-88", "marshmallow-code__apispec.8b421526.lm_rewrite__0s5tgomb", "cdent__gabbi-292", "gruns__icecream.f76fef56.lm_rewrite__qcka30da", "benoitc__gunicorn.bacbf8aa.lm_rewrite__khl83dqp", "fossasia__open-event-server-6425", "paramiko__paramiko.23f92003.lm_rewrite__8noc27ri", "cookiecutter__cookiecutter.b4451231.lm_rewrite__eu4vqp7f", "modin-project__modin-5788", "marshmallow-code__apispec.8b421526.lm_rewrite__o1y1pcqm", "spack__spack-31180", "kivy__python-for-android-2123", "pallets__jinja.ada0a9a6.func_pm_ctrl_invert_if__m2an5l1r", "modin-project__modin.8c7799fd.pr_7225", "getsentry__sentry-python-381", "fniessink__next-action-145", "tweepy__tweepy.91a41c6e.func_pm_remove_assign__puo4sod0", "marshmallow-code__webargs.dbde72fe.lm_rewrite__wcse135b", "iterative__dvc-2554", "darosior__python-bip32-10", "edgedb__edgedb-6268", "spack__spack-4646", "Qiskit__qiskit-6417", "lepture__mistune.bf54ef67.lm_rewrite__jk63q6xa", "docker__compose-483", "stanfordnlp__string2string.c4a72f59.func_pm_remove_cond__jot9qjex", "hpcaitech__ColossalAI-3420", "conan-io__conan.86f29e13.lm_rewrite__i9b5ulv6", "pyupio__safety.7654596b.lm_rewrite__4dskd93y", "azavea__raster-vision-990", "DakaraProject__dakara-base-21", "vyperlang__vyper-2231", "sunpy__sunpy-4088", "agronholm__exceptiongroup.0b4f4937.lm_rewrite__bkxsvzn4", "PennyLaneAI__pennylane-3266", "spack__spack-8831", "iterative__dvc.1d6ea681.lm_rewrite__ymzgvwgl", "conan-io__conan-center-index-3985", "praw-dev__praw-888", "saleor__saleor-2803", "kutaslab__fitgrid-52", "zopefoundation__zope.configuration-37", "svthalia__concrexit-1676", "mne-tools__mne-python-2955", "mir-dataset-loaders__mirdata-249", "graphql-python__graphene.82903263.combine_file__3bt5lzxq", "pyca__pyopenssl.04766a49.lm_rewrite__3bd4k4je", "spack__spack-12086", "matrix-org__synapse-8457", "goauthentik__authentik-6745", "stanfordnlp__dspy.651a4c71.lm_rewrite__pocotluj", "facebookresearch__hydra.0f03eb60.lm_rewrite__05t2speq", "ansible-collections__community.general-2880", "mindsdb__mindsdb-915", "scikit-image__scikit-image-4172", "AnalogJ__lexicon-447", "kayak__pypika.1c9646f0.lm_rewrite__toclgqap", "getmoto__moto-5865", "sosreport__sos-2872", "pretix__pretix-1777", "comic__grand-challenge.org-1234", "Project-MONAI__MONAI.a09c1f08.lm_rewrite__p9a6jqjj", "celery__celery-6866", "fossasia__open-event-server-4445", "bridge<PERSON><PERSON><PERSON>__checkov-831", "modin-project__modin-2457", "joblib__joblib-277", "Anselmoo__spectrafit-660", "repoze__repoze.retry-9", "pulp__pulpcore-4187", "PyCQA__flake8.cf1542ce.lm_rewrite__bt6tbjcp", "Pyomo__pyomo-1772", "regionmask__regionmask-373", "dask__dask-3021", "ansible__ansible-modules-core-5678", "xorbitsai__inference-777", "getsentry__responses-455", "Lightning-AI__lightning-1425", "dbt-labs__dbt-core-2341", "ansible__ansible-lint-318", "conda__conda-11036", "apluslms__a-plus-1218", "pyro-ppl__pyro-2228", "Textualize__textual-2621", "wemake-services__wemake-python-styleguide-1694", "feast-dev__feast-244", "mido__mido.a0158ff9.lm_rewrite__w43pylr1", "dask__dask.5f61e423.lm_rewrite__ea1vaglm", "docker__compose-5054", "marshmallow-code__webargs-123", "wright-group__WrightTools-360", "seperman__deepdiff.ed252022.lm_rewrite__htdfdt7t", "elastic__apm-agent-python-1203", "shadowmoose__pyderman-20", "enthought__chaco-589", "luozhouyang__python-string-similarity.115acaac.combine_file__jzax0e58", "fossasia__open-event-server-4705", "spack__spack-6774", "spack__spack-20794", "akvo__akvo-rsr-1861", "jitsi__jiwer-63", "patrick<PERSON><PERSON>__buildH-80", "pallets__click.fde47b4b.lm_rewrite__cy7wyn7t", "paramiko__paramiko.23f92003.func_basic__u0wx4yvc", "pygments__pygments.27649ebb.lm_rewrite__5ruu5168", "zalando-stups__senza-349", "lincolnloop__python-qrcode.456b01d4.func_pm_ctrl_invert_if__rd<PERSON><PERSON>cz", "tkrajina__gpxpy.09fc46b3.lm_rewrite__r3lxnko4", "searx__searx-1594", "RDFLib__rdflib-1022", "spack__spack-4255", "python-poetry__poetry-277", "HypothesisWorks__hypothesis-1733", "celery__billiard-372", "sopel-irc__sopel-1249", "ivy-llc__ivy-17443", "kserve__kserve-2899", "cupy__cupy-6118", "docker__compose-5155", "google__jax-18075", "bokeh__bokeh-1434", "pex-tool__pex-2325", "Lightning-AI__torchmetrics-2573", "bridge<PERSON><PERSON><PERSON>__checkov-5468", "marshmallow-code__marshmallow.9716fc62.lm_rewrite__xysb3s2e", "ansible__molecule-659", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__wbgzkz65", "Gallopsled__pwntools-157", "andycasey__ads-64", "Pycord-Development__pycord-1250", "great-expectations__great_expectations-6366", "qtile__qtile-4045", "getpelican__pelican-449", "scikit-image__scikit-image-1568", "zalando-stups__senza-462", "DCC-Lab__RayTracing-176", "digitalfabrik__integreat-cms-175", "conda__conda-3683", "nilearn__nilearn-918", "WordPress__openverse-api-599", "GoogleCloudPlatform__httplib2shim-9", "DataDog__dd-trace-py-984", "zopefoundation__zope.index-23", "psychopy__psychopy-4765", "HypothesisWorks__hypothesis-2227", "keleshev__schema.24a30457.lm_rewrite__ls9sqktq", "da<PERSON><PERSON><PERSON>__parso.338a5760.lm_rewrite__clv89rlv", "pydicom__pydicom.7d361b3d.lm_rewrite__x3hktmn9", "DataDog__dd-trace-py-959", "ansible__ansible-modules-core-5106", "searx__searx-333", "nautobot__nautobot-2676", "inducer__relate-169", "getredash__redash-2062", "scverse__scanpy-1637", "opsdroid__opsdroid-1260", "hydroshare__hydroshare-4798", "Textualize__textual-2393", "encode__starlette.db5063c2.lm_rewrite__am93910m", "googleapis__python-spanner-django-33", "cevoaustralia__aws-google-auth-71", "python-hyper__h11.bed0dd4a.lm_rewrite__ezw2t7p9", "iterative__dvc.1d6ea681.lm_rewrite__b51etatr", "docarray__docarray-296", "Mailu__Mailu-2975", "zopefoundation__Products.CMFCore-110", "lhotse-speech__lhotse-240", "paperless-ngx__paperless-ngx-2057", "lk-geim<PERSON>__mimesis-282", "celery__celery-5915", "localstack__localstack-332", "Scille__umongo-190", "freedomofpress__securedrop-354", "ansible__ansible-37308", "planetlabs__planet-client-python-167", "netbox-community__netbox-5578", "kayak__pypika.1c9646f0.lm_rewrite__wxdcdolo", "marshmallow-code__webargs.dbde72fe.lm_rewrite__fvm2qd9t", "cal-itp__benefits-209", "<PERSON><PERSON><PERSON><PERSON><PERSON>__jsons-79", "sunpy__sunpy-4067", "spack__spack-21520", "pytorch__tnt-101", "huggingface__transformers-14316", "digitalfabrik__integreat-cms-2687", "mampfes__hacs_waste_collection_schedule-811", "huggingface__transformers-19898", "getpelican__pelican-2393", "Gallopsled__pwntools-1129", "getnikola__nikola.0f4c230e.lm_rewrite__4f35k5zg", "theskumar__python-dotenv-407", "cookiecutter__cookiecutter.b4451231.lm_rewrite__l5ekmd58", "bentoml__BentoML-4665", "evennia__evennia-3018", "cookiecutter__cookiecutter.b4451231.lm_rewrite__t7glbm0e", "iterative__dvc-2248", "pyupio__safety.7654596b.lm_rewrite__w0oi8ko7", "ckan__ckan-5644", "huggingface__diffusers-1149", "sql-machine-learning__elasticdl-1594", "Parsl__parsl-2259", "cupy__cupy-2657", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__9ah6ud8m", "elastic__apm-agent-python-1134", "rmarkello__abagen-106", "HIPS__autograd.ac044f0d.lm_rewrite__3kli7885", "python-trio__trio.cfbbe2c1.lm_rewrite__7fzhw4bv", "alltheplaces__alltheplaces-3312", "geopandas__geopandas-765", "raiden-network__raiden-contracts-1213", "mindee__doctr-240", "apache__airflow-34931", "PrefectHQ__prefect-1583", "ansible__awx-14489", "r<PERSON><PERSON>__alive-progress.35853799.lm_rewrite__poxyj3wa", "spack__spack-2022", "stanfordnlp__dspy.651a4c71.lm_rewrite__p7nxf274", "pydantic__pydantic.acb0f10f.pr_8316", "pylint-dev__astroid.b114f6b5.pr_2583", "fossasia__open-event-server-7122", "lenskit__lkpy-312", "Qiskit__qiskit-7005", "getsentry__sentry-python-484", "pulp__pulpcore-4427", "encode__starlette.db5063c2.lm_rewrite__ummq40rr", "matrix-org__synapse-4844", "Lightning-AI__lightning-1824", "pantsbuild__pants-11799", "jd__tenacity.0d40e76f.func_basic__hwyzze0l", "dotkom__onlineweb4-486", "rucio__rucio-5795", "ansible__ansible-29109", "cantools__cantools.0c6a7871.lm_rewrite__5cr0n0ys", "pyca__cryptography-1512", "DistrictDataLabs__yellowbrick-427", "wagtail__wagtail-1022", "pyupio__safety.7654596b.lm_rewrite__gtbq1nd1", "django-hijack__django-hijack-334", "mesonbuild__meson-2189", "mne-tools__mne-python-6578", "PyCQA__flake8.cf1542ce.lm_rewrite__fppb6kpl", "singing<PERSON>boy__flask-dance-151", "nilearn__nilearn-2670", "PrefectHQ__prefect-2347", "liqd__a4-me<PERSON><PERSON><PERSON>-532", "OpenMined__PySyft-286", "scoutapp__scout_apm_python-715", "gawel__pyquery.811cd048.lm_rewrite__yk0q9vzr", "tkrajina__gpxpy.09fc46b3.lm_rewrite__fwrz4yov", "mesonbuild__meson-2760", "spack__spack-4912", "hpcaitech__ColossalAI-5228", "beeware__briefcase-2035", "pyca__pyopenssl.04766a49.lm_rewrite__55pbx8e4", "scanny__python-pptx.278b47b1.lm_rewrite__r1lpv0si", "modoboa__modoboa-2495", "qiskit-community__qiskit-aqt-provider-145", "ipython__ipython-1935", "burnash__gspread-1299", "scrapy__scrapy.35212ec5.lm_rewrite__4oufd4mp", "mne-tools__mne-python-5256", "certbot__certbot-2939", "numpy__numpy-11708", "catalystneuro__neuroconv-761", "open-mmlab__mmaction2-624", "numpy__numpy-6556", "keleshev__schema.24a30457.lm_rewrite__rrsc35n6", "mitmproxy__mitmproxy-2665", "jazzband__django-simple-history-1188", "conan-io__conan-3600", "graphql-python__graphene-752", "facebookresearch__hydra.0f03eb60.lm_rewrite__004ka3pp", "archlinux__archinstall-2178", "PrefectHQ__prefect-908", "kayak__pypika.1c9646f0.lm_rewrite__t0uxgex2", "streamlink__streamlink-6439", "christ<PERSON><PERSON>berg__canopen-506", "a<PERSON><PERSON><PERSON>__anyio-732", "dask__dask.5f61e423.lm_rewrite__1s4jcgya", "lutris__lutris-2653", "dbt-labs__dbt-core-8587", "Qiskit__qiskit-2351", "paramiko__paramiko.23f92003.lm_rewrite__tzbnn639", "oasis-open__cti-python-stix2-127", "rapidpro__rapidpro-python-66", "ipython__ipython-3487", "elastic__apm-agent-python-649", "open-telemetry__opentelemetry-python-915", "modin-project__modin-1510", "scikit-hep__uproot5-395", "lutris__lutris-4580", "wtbarnes__fiasco-316", "Lightning-<PERSON>__pytorch-lightning-3404", "pygments__pygments.27649ebb.lm_rewrite__cif6cule", "apache__airflow-17003", "jd__tenacity-236", "spack__spack-6392", "scrapy__scrapy.35212ec5.lm_rewrite__ix9hxkzw", "replicate__cog-653", "pallets__click-2556", "sunpy__sunpy-2572", "pulp__pulpcore-3668", "sqlfluff__sqlfluff.50a1c4b6.lm_rewrite__tjf71s78", "Qiskit__qiskit-2971", "deis__deis-1517", "sqlfluff__sqlfluff-2998", "HypothesisWorks__hypothesis-601", "wagtail__wagtail-730", "google__jax-233", "ansible__ansible-37942", "certbot__certbot-979", "google__jax-2561", "googleapis__python-bigquery-1999", "projectmesa__mesa-373", "elastic__apm-agent-python-1593", "PyCQA__pyflakes-452", "sunpy__sunpy-1346", "pantsbuild__pants-11711", "lepture__mistune.bf54ef67.lm_rewrite__tuj5fugp", "seperman__deepdiff.ed252022.lm_rewrite__8wsf8mam", "ESMCI__cime-1024", "django-money__django-money.835c1ab8.lm_rewrite__bkfaa0ob", "scrapy__scrapy.35212ec5.lm_rewrite__ytwpy23b", "mdn__kuma-5972", "NeuroTechX__moabb-278", "hynek__structlog-594", "cornellius-gp__gpytorch-1468", "Lightning-AI__litdata-461", "DataDog__dd-agent-1401", "pytest-dev__pytest-django-1047", "oasis-open__cti-python-stix2-605", "hpcaitech__ColossalAI-5706", "rootpy__rootpy-297", "spyder-ide__spyder-8790", "encode__starlette.db5063c2.lm_rewrite__2n3w879i", "spulec__freezegun.5f171db0.pr_489", "geopandas__geopandas-591", "nickstenning__honcho-218", "lepture__mistune.bf54ef67.lm_rewrite__1hqujx7q", "stanfordnlp__string2string.c4a72f59.func_pm_op_change_const__sslpkauj", "mabel-dev__opteryx-1461", "interlegis__sapl-521", "huggingface__transformers-20662", "pseudonym117__Riot-Watcher-118", "mozillazg__python-pinyin.e42dede5.lm_rewrite__vrg4ge0m", "zopefoundation__RestrictedPython-137", "poliastro__poliastro-52", "fossasia__open-event-server-4322", "spack__spack-6523", "PyCQA__flake8.cf1542ce.lm_rewrite__duachdou", "lepture__mistune.bf54ef67.lm_rewrite__nojpj426", "microsoft__Qcodes-518", "Qiskit__qiskit-2520", "jsvine__pdfplumber.02ff4313.lm_rewrite__dbfvmo3i", "bokeh__bokeh-6488", "python-pillow__<PERSON>llow-906", "uccser__verto-192", "kivy__python-for-android-2842", "pygments__pygments.27649ebb.lm_rewrite__gin1ggep", "voxel51__fiftyone-2441", "tefra__xsdata-826", "Project-MONAI__MONAI-2398", "Project-MONAI__MONAI.a09c1f08.lm_rewrite__xf99iyzn", "tobymao__sqlglot-808", "mars-project__mars-82", "paramiko__paramiko.23f92003.lm_rewrite__ssrq1qxe", "mozmeao__basket-1374", "phanrahan__magma-980", "pantsbuild__pants-4661", "MongoEngine__mongoengine-1871", "pydicom__pydicom-1995", "kserve__kserve-1137", "ipython__ipython-4363", "zigpy__zha-device-handlers-664", "rucio__rucio-4601", "oauthlib__oauthlib.1fd52536.lm_rewrite__wu7rhd9m", "dask__dask.5f61e423.pr_10521", "ansible__ansible-36592", "TencentBlueKing__bk-user-805", "bridge<PERSON><PERSON><PERSON>__checkov-1102", "freedomofpress__securedrop-4865", "ansible__ansible-11732", "jmoiron__humanize-123", "WordPress__openverse-api-723", "chainer__chainer-812", "borgbackup__borg-246", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__70ycmv6s", "jd__tenacity.0d40e76f.func_pm_ctrl_shuffle__ld25mubm", "napari__napari-2930", "ipython__ipython-4762", "tkrajina__gpxpy.09fc46b3.func_pm_remove_cond__7kf0ctvv", "encode__starlette.db5063c2.lm_rewrite__ywfhvlqa", "kayak__pypika.1c9646f0.lm_rewrite__ni6rum9x", "evennia__evennia-1402", "dandi__dandi-cli-96", "spack__spack-10720", "pulp__pulpcore-4090", "alltheplaces__alltheplaces-8238", "Netflix__lemur-455", "xorbitsai__inference-87", "canonical__microk8s-2048", "spack__spack-3482", "python-poetry__poetry-2787", "mozilla__bugbug-1251", "WeblateOrg__weblate-2306", "oppia__oppia-8711", "mathesar-foundation__mathesar-2361", "mdn__kuma-6595", "marshmallow-code__apispec.8b421526.lm_rewrite__atbwe7em", "mido__mido.a0158ff9.func_pm_class_rm_funcs__3fmubvn7", "xonsh__xonsh-863", "openstates__openstates-scrapers-1346", "mido__mido.a0158ff9.lm_rewrite__wv9flalz", "dbt-labs__dbt-core-1295", "bokeh__bokeh-8670", "google__turbinia-1017", "firebase__firebase-admin-python-162", "mindsdb__mindsdb-1749", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__6fgwwl4n", "scanny__python-pptx.278b47b1.func_basic__msfieasq", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__xbfrfvzq", "mne-tools__mne-python-8554", "nerfstudio-project__nerfstudio-1919", "huggingface__transformers-15835", "pisk<PERSON>ky__gensim-1884", "pulp__pulpcore-4096", "tarioch__xirr-15", "alltheplaces__alltheplaces-3342", "ManageIQ__integration_tests-471", "kayak__pypika.1c9646f0.lm_rewrite__z28zlqjw", "dbt-labs__dbt-core-2376", "xonsh__xonsh-4812", "ansible__ansible-37910", "pallets__jinja.ada0a9a6.lm_rewrite__utj59eqp", "lepture__mistune.bf54ef67.lm_rewrite__femzggxw", "dotkom__onlineweb4-836", "cknd__stackprinter.219fcc52.lm_rewrite__qhd8y367", "nextcloud__appstore-715", "spack__spack-3022", "WordPress__openverse-api-932", "pytorch__ignite-410", "pallets__jinja.ada0a9a6.lm_rewrite__ee0mh704", "interlegis__sapl-2091", "PyCQA__flake8.cf1542ce.lm_rewrite__grfj70s6", "jsvine__pdfplumber.02ff4313.lm_rewrite__ymkj3150", "pydicom__pydicom-463", "getnikola__nikola-1468", "un33k__python-slugify.872b3750.combine_file__u8635nxq", "biolab__orange3-4424", "goauthentik__authentik-4829", "pyca__cryptography-4035", "sopel-irc__sopel-1797", "freedomofpress__securedrop-516", "<PERSON><PERSON><PERSON>I__mycroft-core-845", "er<PERSON><PERSON>__parsimonious.0d3f5f93.lm_rewrite__ls3fh182", "deis__deis-315", "getmoto__moto-744", "wagtail__wagtail-9533", "pymedusa__Medusa-1684", "docker__compose-5634", "kovidgoyal__kitty-5939", "jsvine__pdfplumber.02ff4313.lm_rewrite__n3xadxyx", "jupyterlab__jupyterlab-8835", "mitodl__ocw-data-parser-152", "jsvine__pdfplumber.02ff4313.lm_rewrite__0vvpe6oi", "zalando-stups__senza-535", "google__turbinia-743", "shuup__shuup-1977", "oasis-open__cti-python-stix2-358", "netbox-community__netbox-8767", "freedomofpress__securedrop-5549", "talonhub__community-324", "mars-project__mars-1386", "pulp__pulpcore-4296", "deshima-dev__decode-14", "DDMAL__CantusDB-156", "deepset-ai__haystack-6460", "modin-project__modin-2774", "astropenguin__xarray-dataclasses-115", "cisagov__manage.get.gov-1071", "modin-project__modin-5783", "elastic__apm-agent-python-1161", "spack__spack-703", "microsoft__Qcodes-610", "bridge<PERSON><PERSON><PERSON>__checkov-2740", "mars-project__mars-221", "spyder-ide__spyder-8584", "CTFd__CTFd-1925", "mozilla__bleach.73871d76.lm_rewrite__g09qzt6r", "pypa__twine-412", "LibraryOfCongress__concordia-581", "GPflow__GPflow-2096", "celery__celery-7609", "getsentry__sentry-python-2364", "Cog-Creators__Red-DiscordBot-6197", "networkx__networkx-1407", "borgbackup__borg-5374", "mars-project__mars-1872", "psychopy__psychopy-3812", "python-openxml__python-docx.0cf6d71f.func_basic__sdqjq7el", "pytorch__text-408", "mlflow__mlflow-7581", "bokeh__bokeh-8816", "nylas__nylas-python-98", "svthalia__concrexit-3155", "benoitc__gunicorn.bacbf8aa.lm_rewrite__xh1atmus", "coddingtonbear__python-measurement-41", "sanic-org__sanic-2737", "stanfordnlp__dspy.651a4c71.lm_rewrite__mxkupcvr", "pyupio__safety.7654596b.func_pm_op_change__4hhua3rz", "nilearn__nilearn-3424", "pystorm__pystorm-21", "pypa__setuptools_scm-277", "Lightning-AI__lightning-74", "stanfordnlp__dspy.651a4c71.func_pm_op_change__6bhmx9lp", "getmoto__moto.694ce1f4.func_pm_ctrl_invert_if__s3yti340", "opendatacube__datacube-core-281", "getnikola__nikola.0f4c230e.lm_rewrite__57clvdod", "conan-io__conan-center-index-13306", "ofek__bit-24", "google__clusterfuzz-189", "matthewwithanm__python-markdownify.6258f5c3.lm_rewrite__7zbomkh9", "voxel51__fiftyone-563", "huggingface__transformers-23869", "matrix-org__synapse-5974", "CTFd__CTFd-1830", "deepset-ai__haystack-1933", "HIPS__autograd.ac044f0d.lm_rewrite__oq92pg6q", "mars-project__mars-1623", "facebookresearch__hydra.0f03eb60.lm_rewrite__zl8jehhm", "gradio-app__gradio-724", "google__flax-2591", "ioos__compliance-checker-997", "Project-MONAI__MONAI-5908", "pandas-dev__pandas-5948", "jsvine__pdfplumber.02ff4313.lm_rewrite__r648ffcj", "google__textfsm.c31b6007.combine_file__vzt34nvz", "mplanchard__pydecor-20", "ansible__ansible-21150", "adrienverge__yamllint.8513d9b9.lm_rewrite__hnkv16gi", "Qiskit__qiskit-10057", "alibaba__FederatedScope-496", "marshmallow-code__marshmallow.9716fc62.lm_rewrite__91z9ih3j", "facebookresearch__fvcore.a491d5b9.combine_file__s4s6z0sh", "opendatacube__datacube-core-875", "tobymao__sqlglot-224", "fossasia__open-event-server-2772", "conda__conda-build-862", "qtile__qtile-1696", "microsoft__DeepSpeed-3137", "linkml__linkml-runtime-183", "pdfminer__pdfminer.six.1a8bd2f7.func_pm_remove_wrapper__xr92831y", "huggingface__transformers-21879", "python-pillow__Pillow-7151", "deshima-dev__decode-8", "mido__mido.a0158ff9.combine_file__mlmasre0", "django-money__django-money.835c1ab8.lm_rewrite__io30fgmi", "matrix-org__synapse-12781", "rucio__rucio-623", "joblib__joblib-297", "djangopackages__djangopackages-725", "pyupio__safety.7654596b.func_basic__ufvxw9yr", "Qiskit__qiskit-9051", "iterative__dvc-5741", "airbrake__pybrake-63", "sloria__environs-192", "CTFd__CTFd-1726", "python-trio__trio-2334", "cdrie<PERSON><PERSON>__django-simple-email-auth-31", "sphinx-gallery__sphinx-gallery-890", "pyupio__safety.7654596b.lm_rewrite__1nj7fsg7", "stanfordnlp__string2string.c4a72f59.func_pm_op_change__ca9hwegq", "facebookresearch__hydra.0f03eb60.lm_rewrite__ed6ai5z7", "readthedocs__readthedocs.org-2712", "searx__searx-175", "cornellius-gp__gpytorch-1095", "Ki<PERSON>__kinto-1342", "conan-io__conan.86f29e13.lm_rewrite__grvvsojf", "readthedocs__readthedocs.org-4853", "bokeh__bokeh-1950", "conda__conda-build-2451", "benoitc__gunicorn.bacbf8aa.lm_rewrite__dg2zmzv8", "pallets__click-774", "docker__compose-4016", "buildbot__buildbot-3579", "lamenezes__simple-model-28", "DOV-Vlaanderen__pydov-79", "conan-io__conan-center-index-2833", "omry__omegaconf-57", "Gallopsled__pwntools-1540", "PyCQA__flake8.cf1542ce.lm_rewrite__otym3sbp", "mlcommons__GaNDLF-361", "aws__aws-cli-1526", "pyupio__safety.7654596b.lm_rewrite__c9947nrw", "astropy__ccdproc-757", "spesmilo__electrum-2164", "certbot__certbot-2665", "alanjds__drf-nested-routers.6144169d.func_basic__2iku48rf", "Textualize__textual-4464", "deepset-ai__haystack-7205", "stanfordnlp__dspy.651a4c71.func_pm_remove_assign__sjn<PERSON><PERSON>j", "django-extensions__django-extensions-758", "AlexsLemonade__refinebio-3299", "modin-project__modin-3308", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__dfhx28f4", "bokeh__bokeh-7798", "fossasia__open-event-server-5869", "nipy__nitime-189", "unt-libraries__edtf-validate-30", "spack__spack-11371", "lepture__mistune.bf54ef67.lm_rewrite__cpwac0d5", "conan-io__conan-center-index-2230", "conan-io__conan.86f29e13.lm_rewrite__2bl9jhvj", "translate__pootle-5179", "PennyLaneAI__pennylane-5028", "python-trio__trio.cfbbe2c1.lm_rewrite__tjsavu3i", "netbox-community__netbox-15611", "qutebrowser__qutebrowser-980", "napari__napari-1725", "django-cms__django-cms-2848", "byllyfish__precis_i18n-32", "napari__napari-160", "scikit-hep__awkward-2274", "dask__dask.5f61e423.pr_8954", "pre-commit__pre-commit-310", "facelessuser__soupsieve.a8080d97.lm_rewrite__zhgi9ftk", "Nitrate__Nitrate-607", "CTFd__CTFd-1921", "tensorflow__models-2649", "pulp__pulpcore-2938", "bridge<PERSON><PERSON><PERSON>__checkov-321", "django__channels.a144b4b8.lm_rewrite__9vzepjtr", "sigmavirus24__github3.py-407", "liqd__a4-me<PERSON><PERSON>lin-2170", "cantools__cantools.0c6a7871.lm_rewrite__fmjg6jnb", "fossasia__open-event-server-4147", "lepture__mistune.bf54ef67.lm_rewrite__p0mp36c7", "arrow-py__arrow.1d70d009.lm_rewrite__5lfbjnez", "pydicom__pydicom.7d361b3d.lm_rewrite__z6w6qfsr", "apache__tvm-2119", "ansible-collections__community.general-2844", "celery__celery-6259", "pyupio__safety.7654596b.lm_rewrite__h82m3575", "stanfordnlp__dspy.651a4c71.lm_rewrite__sp9sdq5y", "jawah__charset_normalizer.1fdd6463.lm_rewrite__895ttfle", "typeddjango__django-stubs-421", "Qiskit__qiskit-8456", "pydicom__pydicom.7d361b3d.lm_rewrite__vxq7tp2k", "aws-cloudformation__cfn-lint-962", "jboss-docker<PERSON><PERSON>__dogen-134", "PrefectHQ__prefect-7641", "pre-commit__pre-commit-1359", "cantools__cantools.0c6a7871.func_pm_remove_assign__7lkcr6a8", "Lightning-AI__torchmetrics-1210", "Cog-Creators__Red-DiscordBot-2346", "mesonbuild__meson-4428", "modin-project__modin-446", "biolab__orange3-5006", "jsvine__pdfplumber.02ff4313.lm_rewrite__yf6z8ap9", "matthewwithanm__python-markdownify.6258f5c3.lm_rewrite__3nj3tgd0", "iterative__dvc-1749", "pyupio__safety.7654596b.lm_rewrite__vp82b6ty", "ultralytics__yolov5-8711", "stanfordnlp__dspy.651a4c71.func_pm_op_break_chains__ifljbh46", "mozillazg__python-pinyin.e42dede5.lm_rewrite__jgn0qrio", "cantools__cantools.0c6a7871.lm_rewrite__wk6oc5n1", "numpy__numpy-11843", "modin-project__modin-6098", "googleapis__google-auth-library-python-1243", "networkx__networkx-2995", "apache__airflow-1242", "Cog-Creators__Red-DiscordBot-1981", "lepture__mistune.bf54ef67.lm_rewrite__6bdfy7qz", "Qiskit__qiskit-5735", "<PERSON><PERSON><PERSON>__darker-349", "wright-group__<PERSON><PERSON><PERSON>s-688", "xonsh__xonsh-1788", "da<PERSON><PERSON><PERSON>__parso.338a5760.lm_rewrite__75na5o7h", "lepture__mistune.bf54ef67.lm_rewrite__dfwkww9o", "pyupio__safety.7654596b.lm_rewrite__69lwggz1", "fedora-infra__bodhi-2165", "cleanlab__cleanlab-397", "fairlearn__fairlearn-906", "r<PERSON><PERSON>__alive-progress.35853799.lm_rewrite__5rd94s7b", "holoviz__holoviews-5969", "canonical__charmcraft-1759", "ansible-collections__amazon.aws-1570", "huggingface__optimum-334", "seperman__deepdiff.ed252022.lm_rewrite__57akt828", "pre-commit__pre-commit-622", "DataDog__integrations-core-2041", "sqlfluff__sqlfluff.50a1c4b6.lm_rewrite__poax9ktl", "bookwyrm-social__bookwyrm-375", "ultrabug__py3status-551", "apache__tvm-7327", "mitmproxy__mitmproxy-1904", "python__typeshed-8843", "pulp__pulpcore-4095", "oauthlib__oauthlib.1fd52536.lm_rewrite__1mk7jsct", "sec-edgar__sec-edgar-309", "pyinstaller__pyinstaller-8544", "CTFd__CTFd-1508", "HIPS__autograd.ac044f0d.lm_rewrite__7sy8cnvk", "litestar-org__litestar-3172", "Qiskit__qiskit-8288", "e-valuation__EvaP-1291", "jupyter__nbformat-173", "conan-io__conan-9371", "modin-project__modin-459", "tornadoweb__tornado.d5ac65c1.func_pm_ctrl_invert_if__z0wlut1a", "dask__dask.5f61e423.lm_rewrite__0jf8a01k", "localstack__localstack-9732", "marshmallow-code__webargs.dbde72fe.lm_rewrite__hme01rq8", "keras-team__keras-2268", "ckan__ckan-4525", "Qiskit__qiskit-4331", "WeblateOrg__weblate-3377", "level12__morphi-6", "goauthentik__authentik-4920", "<PERSON><PERSON>__kinto-1040", "pyupio__safety.7654596b.func_pm_remove_assign__cmvy4tcq", "googleapis__python-bigquery-326", "python-pillow__Pillow-5510", "kutaslab__fitgrid-54", "joke2k__faker-924", "Pylons__pyramid-3456", "huggingface__transformers-13859", "docker__compose-3964", "bridge<PERSON><PERSON><PERSON>__checkov-5170", "pennersr__django-allauth-1118", "python-hyper__h11.bed0dd4a.func_basic__lw77utls", "aka<PERSON>ola__darker-545", "conda__conda-9999", "facebookresearch__hydra.0f03eb60.lm_rewrite__u7o6n566", "ansible__ansible-23365", "conan-io__conan-3087", "Flexget__Flexget-1526", "Lightning-<PERSON>__pytorch-lightning-74", "django-money__django-money.835c1ab8.lm_rewrite__rrkc52ei", "numpy__numpy-4300", "Lightning-AI__lightning-1954", "Cog-Creators__Red-DiscordBot.33e0eac7.combine_file__ib9j050b", "fossasia__open-event-server-6010", "dask__dask.5f61e423.lm_rewrite__dks6ihok", "ipython__ipython-4346", "carpentries__amy-81", "spack__spack-10075", "dotkom__onlineweb4-2123", "open-telemetry__opentelemetry-python-contrib-378", "scverse__scanpy-2859", "qtile__qtile-180", "librosa__librosa-1699", "astropenguin__morecopy-38", "mljar__mljar-supervised-769", "mampfes__hacs_waste_collection_schedule-1366", "gruns__icecream.f76fef56.lm_rewrite__wqro9g2d", "dbt-labs__dbt-core-8803", "ansible__molecule-4038", "lepture__mistune.bf54ef67.lm_rewrite__16rgzg4j", "ivy-llc__ivy-27847", "digitalfabrik__integreat-cms-1174", "xonsh__xonsh-1358", "networkx__networkx-3628", "huggingface__transformers-7035", "googleapis__python-bigquery-630", "fjosw__pyer<PERSON>rs-52", "theskumar__python-dotenv-236", "nilearn__nilearn-4150", "strawberry-graphql__strawberry-1348", "jd__tenacity.0d40e76f.lm_rewrite__0siuyh3e", "librosa__librosa-1839", "nautobot__nautobot-763", "deepchecks__deepchecks-1329", "PyCQA__flake8.cf1542ce.lm_rewrite__73z81qv9", "cobbler__cobbler-2819", "python-jsonschema__jsonschema.93e0caa5.lm_rewrite__rnaqpe5p", "pypa__setuptools-3515", "getredash__redash-2951", "dotkom__onlineweb4-1743", "PrefectHQ__prefect-7678", "pytorch__benchmark-396", "openstates__openstates-scrapers-2453", "digitalfabrik__integreat-cms-444", "mne-tools__mne-python-2364", "conda__conda-build-3213", "svthalia__concrexit-1719", "googleapis__python-api-core-178", "getnikola__nikola.0f4c230e.lm_rewrite__2a2ox6ke", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__iv8vwrta", "google__jax-15206", "pydicom__pydicom.7d361b3d.combine_file__j6ulbui7", "fossasia__open-event-server-5658", "sunpy__sunpy-7752", "ResonantGeoData__ResonantGeoData-311", "CWorthy-ocean__C-Star-226", "Pyomo__pyomo-1385", "jawah__charset_normalizer.1fdd6463.lm_rewrite__h2zn5g82", "hedyorg__hedy-1383", "mozillazg__python-pinyin.e42dede5.lm_rewrite__0xvqderw", "scanny__python-pptx.278b47b1.lm_rewrite__p124hs3f", "ckan__ckan-453", "pyupio__safety.7654596b.func_basic__lagy5ip3", "modin-project__modin-6133", "encode__starlette.db5063c2.lm_rewrite__pxxqnp2t", "beetbox__beets-2524", "Project-MONAI__MONAI-2061", "Qiskit__qiskit-1830", "stanfordnlp__string2string.c4a72f59.func_pm_ctrl_invert_if__yc9fso4f", "mlflow__mlflow-6326", "Lightning-AI__pytorch-lightning-1824", "conan-io__conan-5846", "alanjds__drf-nested-routers.6144169d.func_basic__p5v8ubgz", "saleor__saleor-5110", "getnikola__nikola-1886", "PrefectHQ__prefect-2631", "pyexcel__pyexcel-220", "ansible-collections__community.general-8386", "napari__napari-3424", "kornia__kornia-2054", "r<PERSON>mei__alive-progress.35853799.lm_rewrite__e1osomfp", "automl__auto-sklearn-190", "Pyomo__pyomo-2458", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__3zuuo3wo", "bids-standard__pybids-329", "conan-io__conan-center-index-5207", "pydicom__pydicom.7d361b3d.lm_rewrite__yip9mseb", "PrefectHQ__prefect-266", "ASFHyP3__hyp3-sdk-53", "vyperlang__vyper-1338", "PokemonGoF__PokemonGo-Bot-5036", "streamlit__streamlit-2570", "cookiecutter__cookiecutter.b4451231.lm_rewrite__gsks0ek9", "scikit-hep__awkward-3013", "seperman__deepdiff.ed252022.combine_file__24qpwc9c", "bokeh__bokeh-4437", "DataDog__dd-trace-py-2980", "rucio__rucio-2660", "chainer__chainer-289", "Flexget__Flexget-2802", "stanfordnlp__dspy.651a4c71.func_pm_ctrl_shuffle__6i49u5vv", "narwhals-dev__narwhals-1246", "aws-cloudformation__cfn-lint-179", "Qiskit__qiskit-4387", "python-trio__trio.cfbbe2c1.lm_rewrite__w3yih2fg", "martin<PERSON>ch__xmltodict.0952f382.func_basic__4ry5wj8i", "ivy-llc__ivy-28356", "spack__spack-10200", "lepture__mistune.bf54ef67.lm_rewrite__nv6zdix7", "LSSTDESC__healsparse-157", "pyodide__pyodide-2099", "bookwyrm-social__bookwyrm-1605", "ansible__ansible-11628", "nerfstudio-project__nerfstudio-2088", "joke2k__faker-995", "agronholm__typeguard.b6a7e438.lm_rewrite__k97shf45", "asdf-format__asdf-1575", "Lightning-AI__lightning-2356", "lutris__lutris-3623", "graphql-python__graphene.82903263.combine_file__4790hhf2", "pydicom__pydicom.7d361b3d.lm_rewrite__r555lkr2", "getmoto__moto-2158", "docker__compose-3269", "certbot__certbot-2478", "beeware__briefcase-1824", "plone__Products.CMFPlone-2326", "bokeh__bokeh-5433", "biopython__biopython-3247", "paramiko__paramiko.23f92003.lm_rewrite__4xxvbe6j", "Midnighter__dependency-info-6", "arviz-devs__arviz-2140", "ivy-llc__ivy-13987", "learningequality__kolibri-6194", "microsoft__torchgeo-1647", "gammapy__gammapy-2263", "open-telemetry__opentelemetry-python-contrib-421", "line__line-bot-sdk-python-274", "python-trio__trio-2648", "agronholm__exceptiongroup.0b4f4937.func_basic__qnbdkttr", "geopandas__geopandas-1445", "jupyterlab__jupyterlab-7582", "bridge<PERSON><PERSON><PERSON>__checkov-3644", "imAsparky__django-cookiecutter-753", "ray-project__ray-3711", "ansible__ansible-30589", "mlenzen__collections-extended-152", "GeotrekCE__Geotrek-admin-1708", "edgedb__edgedb-1946", "open-telemetry__opentelemetry-python-2576", "ARMmbed__mbed-tools-259", "python-poetry__poetry-8649", "mmaelicke__scikit-gstat-158", "pandas-dev__pandas-7085", "Pyomo__pyomo-996", "microsoft__botbuilder-python-1727", "pennmem__cmlreaders-195", "alexgolec__tda-api-129", "pyca__pyopenssl.04766a49.lm_rewrite__of64o1dn", "reata__sqllineage-22", "dbt-labs__dbt-core-5356", "pallets__click-2576", "conda__conda-12756", "pyinstaller__pyinstaller-1861", "pallets__click.fde47b4b.lm_rewrite__dcme2i6c", "xen0l__aws-gate-599", "facebookresearch__hydra.0f03eb60.lm_rewrite__n8cqra9c", "mesonbuild__meson-11729", "OpenNMT__OpenNMT-tf-362", "docker__compose-2295", "tornadoweb__tornado.d5ac65c1.lm_rewrite__rl4yuxyq", "pyupio__safety.7654596b.lm_rewrite__hzd9v10g", "marshmallow-code__webargs.dbde72fe.lm_rewrite__xwzv8hf2", "bokeh__bokeh-3235", "open-mmlab__mmdetection-1099", "python-trio__trio.cfbbe2c1.lm_rewrite__m6s6dapo", "ivy-llc__ivy-16270", "saleor__saleor-2791", "biopython__biopython-4029", "eigenfoo__littlemcmc-63", "pypa__pip-11333", "python-trio__trio.cfbbe2c1.lm_rewrite__76psm5s5", "borgbackup__borg-1199", "dj-stripe__dj-stripe-348", "ipython__ipython-6774", "scikit-hep__awkward-2373", "pymc-devs__pymc-7428", "iterative__dvc-3576", "dask__dask.5f61e423.pr_9378", "dotkom__onlineweb4-1521", "matrix-org__synapse-9070", "arrow-py__arrow.1d70d009.lm_rewrite__c4vurhst", "oppia__oppia-14800", "getsentry__sentry-2688", "streamlink__streamlink-543", "dbt-labs__dbt-core-5028", "agronholm__exceptiongroup.0b4f4937.func_pm_op_swap__pyissygc", "fossasia__open-event-server-5056", "mesonbuild__meson-6689", "wagtail__wagtail-11162", "django-cms__django-cms-3559", "pyutils__line_profiler.a646bf0f.combine_file__n8r8xi03", "pre-commit__pre-commit-376", "marshmallow-code__apispec.8b421526.lm_rewrite__athquo0l", "open-mmlab__mmcv-823", "google__jax-18797", "statsmodels__statsmodels-680", "sql-machine-learning__elasticdl-923", "tornadoweb__tornado.d5ac65c1.func_pm_ctrl_invert_if__8wu0zwm3", "scikit-image__scikit-image-1903", "searx__searx-3444", "openmc-dev__openmc-485", "ansible-collections__community.general-918", "pydicom__pydicom.7d361b3d.lm_rewrite__2gimskcb", "PaddlePaddle__PaddleSpeech-153", "DataDog__datadog-lambda-python-214", "nautobot__nautobot-5388", "searxng__searxng-2862", "swcarpentry__python-novice-inflammation-946", "geomstats__geomstats-1874", "Lightning-AI__lightning-3404", "ivy-llc__ivy-19373", "Lightning-AI__torchmetrics-1649", "matrix-org__synapse-12803", "SALib__SALib-636", "pre-commit__pre-commit-335", "electricitymaps__electricitymaps-contrib-1631", "PyGith<PERSON>__PyGithub-1922", "scrapy__itemadapter-18", "vyperlang__vyper-3174", "MITLibraries__oastats-backend-46", "litestar-org__litestar-2864", "plone__Products.CMFPlone-3133", "ansible-collections__community.general-4061", "lightstep__otel-launcher-python-93", "wagtail__wagtail-7702", "mesonbuild__meson-2840", "sopel-irc__sopel-943", "dbt-labs__dbt-core-8641", "cal-itp__benefits-922", "conan-io__conan-203", "PyCQA__flake8-1642", "mozilla__bleach.73871d76.lm_rewrite__kmzfk5md", "pre-commit__pre-commit-86", "rucio__rucio-2659", "dask__dask-1693", "planetlabs__planet-client-python-129", "bokeh__bokeh-3332", "getpelican__pelican-2440", "spyder-ide__spyder-8349", "tefra__xsdata-1010", "pre-commit__pre-commit-575", "cdent__gabbi-56", "deepset-ai__haystack-5503", "mozilla__bleach.73871d76.lm_rewrite__bd9ti9bc", "ARM-DOE__ACT-837", "modin-project__modin-2781", "kayak__pypika.1c9646f0.lm_rewrite__2akfu7cf", "facebookresearch__hydra.0f03eb60.lm_rewrite__pl3fyb19", "Cog-Creators__Red-DiscordBot-5393", "facebookresearch__fvcore.a491d5b9.lm_rewrite__pcccevul", "huggingface__transformers-24942", "svthalia__concrexit-1802", "repobee__repobee-371", "digitalfabrik__integreat-cms-823", "pyodide__pyodide-2939", "pygments__pygments.27649ebb.lm_rewrite__f0ohbwg2", "huggingface__transformers-12350", "iterative__dvc-4185", "paperless-ngx__paperless-ngx-849", "buildbot__buildbot-1614", "huggingface__transformers-22031", "Lightning-AI__lightning-2405", "python-gitlab__python-gitlab-2771", "kayak__pypika.1c9646f0.********************", "xonsh__xonsh-4952", "privacyidea__privacyidea-3231", "ARMmbed__mbed-tools-91", "cta-observatory__pyirf-212", "pydicom__pydicom.7d361b3d.lm_rewrite__d9jh70r0", "beetbox__beets-4943", "docker__docker-py-1801", "PrefectHQ__prefect-176", "ipython__ipython-2500", "pydicom__pydicom.7d361b3d.lm_rewrite__onjbg888", "spack__spack-3828", "pyca__pyopenssl.04766a49.lm_rewrite__sck7w3xu", "stanfordnlp__string2string.c4a72f59.func_pm_op_change_const__js80kw75", "KIT-IAI__pyWATTS-261", "python-pillow__Pillow-7112", "graphql-python__graphene-644", "numba__numba-2728", "kayak__pypika.1c9646f0.lm_rewrite__xyarbbsa", "python-trio__trio.cfbbe2c1.lm_rewrite__xs832beh", "sql-machine-learning__elasticdl-2180", "modin-project__modin-535", "seperman__deepdiff.ed252022.lm_rewrite__6kwonvhg", "PrefectHQ__prefect-849", "facebookresearch__fvcore.a491d5b9.lm_rewrite__1lp7kdh2", "openai__openai-python-125", "typesafehub__conductr-cli-466", "European-XFEL__EXtra-data-104", "spack__spack-21909", "lk-geim<PERSON>__mimesis-664", "spack__spack-4901", "pantsbuild__pants-20502", "kayak__pypika.1c9646f0.lm_rewrite__6brymw1j", "meltano__meltano-7371", "google__jax-15458", "Lightning-AI__lightning-2244", "rucio__rucio-3991", "pantsbuild__pants-18259", "pallets__click.fde47b4b.func_pm_ctrl_invert_if__doj3<PERSON>ci", "doccano__doccano-1654", "bugov__aiohttp-basicauth-middleware-7", "pydoit__doit-246", "networkx__networkx-4246", "pyupio__safety.7654596b.lm_rewrite__8qsi8ijn", "Qiskit__qiskit-3751", "gawel__pyquery.811cd048.lm_rewrite__wvy6bykj", "mampfes__hacs_waste_collection_schedule-190", "arrow-py__arrow.1d70d009.lm_rewrite__76bcuvzu", "pypa__pipenv-60", "saleor__saleor-5311", "tornadoweb__tornado-1984", "PennyLaneAI__pennylane-2947", "microsoft__botbuilder-python-302", "scikit-image__scikit-image-4607", "fossasia__open-event-server-5818", "spack__spack-8879", "ansible__ansible-2412", "jd__tenacity.0d40e76f.lm_rewrite__md3042lx", "getnikola__nikola.0f4c230e.func_pm_ctrl_invert_if__bryxs3d0", "airbrake__pybrake-44", "Turbo87__utm-31", "conda__conda-build-5124", "Zeroto521__my-data-toolkit-645", "Lightning-AI__pytorch-lightning-2405", "PrefectHQ__prefect-3345", "carpentries__amy-2358", "chan<PERSON><PERSON><PERSON>__miniwdl-549", "Textualize__rich-3257", "huggingface__transformers-14193", "sqlfluff__sqlfluff-3435", "iterative__dvc-4733", "django-oscar__django-oscar-3489", "paperless-ngx__paperless-ngx-2544", "fossasia__open-event-server-4398", "google__textfsm.c31b6007.func_pm_ctrl_shuffle__pyn9jg7o", "unionai-oss__pandera-22", "mindsdb__mindsdb-1954", "and<PERSON>brecht__sqlparse.e57923b3.combine_file__r9woqbsa", "modin-project__modin-2731", "fossasia__open-event-server-5763", "qutebrowser__qutebrowser-1459", "nltk__nltk-3042", "chardet__chardet.9630f238.lm_rewrite__ljtsbcii", "cupy__cupy-3347", "cloudtools__troposphere-1289", "pwaller__pyfiglet.f8c5f35b.func_basic__zh9g8b3e", "sqlfluff__sqlfluff.50a1c4b6.func_pm_ctrl_invert_if__w8e3m8ag", "tobymao__sqlglot-3975", "conan-io__conan.86f29e13.func_pm_op_change__mhfaaig3", "docker__compose-4324", "pygments__pygments.27649ebb.lm_rewrite__7aoc33n8", "hyperledger__aries-cloudagent-python-1569", "pantsbuild__pants-11606", "mkdocs__mkdocs-955", "rucio__rucio-2463", "pybamm-team__PyBaMM-544", "tox-dev__tox-2024", "mlflow__mlflow-5627", "lutris__lutris-1222", "ARM-software__mango-42", "crytic__slither-786", "google__mobly-518", "spack__spack-19768", "pyg-team__pytorch_geometric-8550", "prowler-cloud__prowler-2639", "openmc-dev__openmc-2388", "scrapy__w3lib-230", "iterative__dvc-4584", "na<PERSON><PERSON><PERSON><PERSON>__aiosql-25", "DDMAL__CantusDB-1167", "tkrajina__gpxpy.09fc46b3.lm_rewrite__o25e7b9w", "matrix-org__synapse-6146", "jazzband__django-axes-911", "deepset-ai__haystack-6765", "ansible__ansible-34221", "spack__spack-5099", "kayak__pypika.1c9646f0.lm_rewrite__zf3zsje2", "django-wiki__django-wiki-1066", "openstates__openstates-scrapers-2439", "pulp__pulpcore-3083", "conda__conda-build-1108", "benoitc__gunicorn.bacbf8aa.lm_rewrite__4twhroug", "astropenguin__xarray-dataclasses-140", "bokeh__bokeh-3572", "conda__conda-4327", "pallets__werkzeug-2103", "globocom__m3u8-375", "conan-io__conan-5337", "doccano__doccano-1668", "freqtrade__freqtrade-10221", "marshmallow-code__webargs.dbde72fe.lm_rewrite__0w9v7bbb", "adrienverge__yamllint.8513d9b9.func_basic__qnpsltuo", "DDMAL__CantusDB-1183", "Qiskit__qiskit-8216", "ASFHyP3__hyp3-sdk-51", "stanfordnlp__dspy.651a4c71.lm_rewrite__yi08i208", "django__channels.a144b4b8.lm_rewrite__ifv4vwtm", "cornellius-gp__gpytorch-1275", "mne-tools__mne-python-2272", "pybamm-team__PyBaMM-962", "xorbitsai__inference-758", "facebookresearch__hydra.0f03eb60.lm_rewrite__zi2u85ps", "oppia__oppia-1600", "conan-io__conan.86f29e13.lm_rewrite__m0121wmw", "HIPS__autograd.ac044f0d.lm_rewrite__3lvf5hot", "vispy__vispy-751", "jazzband__django-debug-toolbar-1349", "jupyterhub__jupyterhub-1413", "openstates__openstates-scrapers-1173", "deepset-ai__haystack-7909", "huggingface__diffusers-3666", "fossasia__open-event-server-6100", "keras-team__keras-8801", "ibis-project__ibis-9037", "pallets__jinja.ada0a9a6.lm_rewrite__wnwzeqi4", "facebookresearch__hydra.0f03eb60.lm_rewrite__k5hiu3cv", "django-json-api__django-rest-framework-json-api-788", "wright-group__<PERSON><PERSON>ools-461", "spack__spack-20020", "cupy__cupy-186", "facebookresearch__hydra.0f03eb60.lm_rewrite__vdktu9sg", "buildbot__buildbot-5156", "jsch<PERSON>ier__django-storages-1281", "tkrajina__gpxpy.09fc46b3.lm_rewrite__7ws8195u", "crytic__slither-475", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__295g9vu1", "Project-MONAI__MONAI-4847", "spack__spack-22059", "Flexget__Flexget-2288", "dbt-labs__dbt-core-2766", "microsoft__AzureTRE-1653", "spack__spack-1778", "iterative__dvc.1d6ea681.lm_rewrite__96dkwfxr", "django__channels.a144b4b8.combine_file__ffhlpysd", "readthedocs__readthedocs.org-4781", "rucio__rucio-1164", "aws__aws-cli-2537", "stanfordnlp__string2string.c4a72f59.func_pm_remove_assign__3vrs9vty", "pydantic__pydantic-5193", "Mailu__Mailu-2299", "Ki<PERSON>__kinto-959", "spacetelescope__jwql-288", "huggingface__diffusers-1938", "keras-team__keras-2980", "pygments__pygments.27649ebb.lm_rewrite__sici23s4", "pantsbuild__pants-15016", "borgbackup__borg-1066", "cupy__cupy-4167", "celery__celery-3952", "numpy__numpy-10739", "tweepy__tweepy.91a41c6e.func_pm_ctrl_invert_if__zmbb12r1", "django-oscar__django-oscar-2337", "archlinux__archinstall-1021", "adrienverge__yamllint.8513d9b9.func_basic__3xzlqi3j", "dask__dask-5066", "omry__omegaconf-71", "pandas-dev__pandas-5486", "ESSS__conda-devenv-46", "pantsbuild__pants-18678", "DataDog__dd-agent-495", "conda__conda-12293", "modin-project__modin-1862", "streamlink__streamlink-925", "pytorch__ignite-2412", "iterative__dvc-1531", "HypothesisWorks__hypothesis-2428", "facebookresearch__hydra.0f03eb60.lm_rewrite__w9cbaiwu", "PrefectHQ__prefect-6903", "google__openhtf-466", "common-workflow-language__cwltool-1504", "napari__napari-6679", "cantools__cantools.0c6a7871.lm_rewrite__wxmvwx55", "googleapis__python-spanner-django-780", "pydicom__pydicom.7d361b3d.lm_rewrite__enb4qzck", "lutris__lutris-1780", "matthewwithanm__python-markdownify.6258f5c3.lm_rewrite__7j5710fm", "jupyterlab__jupyterlab-9515", "ManimCommunity__manim-1368", "<PERSON><PERSON><PERSON>__Zappa-1993", "ibis-project__ibis-3415", "spdx__tools-python-719", "learningequality__kolibri-11775", "scikit-image__scikit-image-1440", "pyupio__safety.7654596b.combine_file__mwteckxl", "mne-tools__mne-python-2987", "oppia__oppia-6362", "facebookresearch__habitat-lab-347", "kedro-org__kedro-1821", "tensorflow__model-optimization-576", "quantumlib__Cirq-335", "biolab__orange3-2376", "openai__gym-233", "Lightning-AI__pytorch-lightning-2055", "SciTools__cartopy-181", "conda__conda-build-1802", "internetarchive__openlibrary-5259", "mne-tools__mne-python-7895", "bokeh__bokeh-9031", "lepture__mistune.bf54ef67.lm_rewrite__z4zik0oz", "marshmallow-code__webargs.dbde72fe.lm_rewrite__7en30ztg", "pymodbus-dev__pymodbus-2103", "pre-commit__pre-commit-hooks-487", "oauthlib__oauthlib.1fd52536.lm_rewrite__knw71m02", "stanfordnlp__dspy.651a4c71.func_pm_remove_cond__lnmmwjzw", "cupy__cupy-498", "googleapis__google-cloud-python-3745", "tweepy__tweepy.91a41c6e.combine_file__lbaq5ndr", "Unidata__MetPy-1956", "ivy-llc__ivy-20185", "django-cms__django-cms-2015", "ivy-llc__ivy-28422", "pydicom__pydicom.7d361b3d.lm_rewrite__xzq5rrbf", "DDMAL__CantusDB-945", "modin-project__modin-3685", "pygments__pygments.27649ebb.lm_rewrite__y7f1zn3q", "hyperledger__aries-cloudagent-python-1667", "ivy-llc__ivy-13823", "keras-team__keras-tuner-657", "ansible-collections__amazon.aws-1597", "keras-team__keras-637", "xonsh__xonsh-4511", "m<PERSON><PERSON>a__overrides-52", "Lightning-AI__pytorch-lightning-1265", "bokeh__bokeh-8651", "pennersr__django-allauth-3283", "spack__spack-4584", "facebookresearch__fvcore.a491d5b9.func_basic__j5myi9rw", "hpcaitech__ColossalAI-4320", "pandas-dev__pandas-16509", "sanic-org__sanic-2128", "CiviWiki__OpenCiviWiki-980", "pyinstaller__pyinstaller-6581", "rucio__rucio-6758", "gawel__pyquery.811cd048.combine_file__ge0uxzm6", "getnikola__nikola.0f4c230e.lm_rewrite__qjc66i7w", "psf__black-3257", "encode__starlette-701", "sopel-irc__sopel-1859", "<PERSON><PERSON>__kinto-1956", "huggingface__diffusers-942", "ansible__ansible-modules-core-5113", "qutip__qutip-2305", "locustio__locust-1194", "kivy__python-for-android-2180", "django__asgiref-211", "psychopy__psychopy-3306", "ipython__ipython-9706", "voicepaw__so-vits-svc-fork-557", "python-tap__tappy-112", "keleshev__schema.24a30457.func_pm_ctrl_invert_if__gz4iiygu", "CTFd__CTFd-2091", "conan-io__conan.86f29e13.func_pm_remove_assign__cpbyygj7", "django-cms__django-cms-2039", "learningequality__kolibri-8449", "spack__spack-43770", "stanfordnlp__string2string.c4a72f59.func_pm_op_change__1gjqcasb", "python-openxml__python-docx.0cf6d71f.combine_file__ygn13uil", "microsoft__knossos-ksc-1027", "burnash__gspread-1427", "psychopy__psychopy-3621", "ipython__ipython-13889", "freedomofpress__securedrop-4872", "statsmodels__statsmodels-3179", "StackStorm__st2-6073", "learningequality__kolibri-11933", "HIPS__autograd.ac044f0d.lm_rewrite__x43lsuap", "ansible__ansible-18759", "ESMValGroup__ESMValCore-2472", "oasis-open__cti-taxii-client-11", "jupyterlab__jupyterlab-3127", "modin-project__modin-5743", "gitpython-developers__GitPython-658", "ipython__ipython-5652", "svthalia__concrexit-1463", "kayak__pypika.1c9646f0.lm_rewrite__kyb8lobh", "closeio__freezefrog-3", "django__asgiref-196", "google-deepmind__dm-haiku-337", "aws-cloudformation__cfn-lint-3017", "PyCQA__flake8.cf1542ce.lm_rewrite__y8hn1d2o", "joke2k__faker-1393", "e-valuation__EvaP-848", "huggingface__transformers-15310", "holoviz__panel-5265", "django__daphne.32ac73e1.lm_rewrite__0lubj1nx", "matrix-org__synapse-14032", "ephios-dev__ephios-364", "vyperlang__vyper-2071", "ansible-collections__community.general-8240", "matrix-org__synapse-6197", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__scuba-137", "mkdocs__mkdocs-2481", "ivy-llc__ivy-14502", "spack__spack-29975", "googleapis__google-auth-library-python-188", "apache__airflow-1247", "scrapy__scrapy.35212ec5.lm_rewrite__v1fq87jd", "HIPS__autograd.ac044f0d.lm_rewrite__p4t98j02", "zopefoundation__persistent-140", "auth0__auth0-python-64", "kartoza__prj.app-677", "Pyomo__pyomo-1014", "pyca__cryptography-2617", "python-trio__trio.cfbbe2c1.func_basic__pkk9jdsc", "mwouts__itables-324", "mkdocs__mkdocs-2710", "paramiko__paramiko.23f92003.lm_rewrite__b230n90o", "mne-tools__mne-python-1491", "web2py__web2py-2099", "pylint-dev__astroid.b114f6b5.combine_file__luvl1fkd", "jupyterlab__jupyterlab-9551", "spack__spack-6437", "mesonbuild__meson-772", "ansible-collections__amazon.aws-2029", "searx__searx-2102", "facebookresearch__hydra.0f03eb60.lm_rewrite__rpnlb3tt", "modin-project__modin-4179", "streamlit__streamlit-2217", "deepset-ai__haystack-2101", "ros__ros_comm-1698", "pytorch__audio-814", "dwavesystems__dimod-459", "django-cms__django-cms-1364", "ethereum__web3.py-1198", "Parsl__parsl-435", "typeddjango__django-stubs-415", "ipython__ipython-6064", "holoviz__holoviews-3804", "rucio__rucio-635", "dask__dask-822", "jupyterlab__jupyterlab-4989", "getmoto__moto.694ce1f4.pr_6387", "python-hyper__h11.bed0dd4a.lm_rewrite__e0j9ai11", "numpy__numpy-24522", "freedomofpress__securedrop-7156", "cleanlab__cleanlab-563", "falconry__falcon-1946", "cloud-custodian__cloud-custodian-2745", "benoitc__gunicorn.bacbf8aa.func_basic__q2okc2dq", "mesonbuild__meson-72", "numpy__numpy-11746", "wemake-services__wemake-python-styleguide-2343", "rsagroup__rsatoolbox-403", "spotify__luigi-893", "pulp__pulpcore-4129", "pyscript__pyscript-1779", "certbot__certbot-3063", "ansible-collections__community.general-8133", "seperman__deepdiff.ed252022.lm_rewrite__ckh4ofx4", "scikit-image__scikit-image-4512", "plotly__dash-346", "jawah__charset_normalizer.1fdd6463.lm_rewrite__7t1ju4wj", "openstates__openstates-scrapers-1272", "lepture__mistune.bf54ef67.lm_rewrite__rvw5rmwe", "pytest-dev__pytest-django-426", "ipython__ipython-10434", "nipy__nipype-3248", "apache__airflow-25553", "goauthentik__authentik-7386", "koaning__bulk-12", "encode__starlette-134", "KIT-IAI__pyWATTS-92", "caronc__apprise-119", "femueller__python-n26-73", "docker__compose-4588", "google__flax-4002", "jsvine__pdfplumber.02ff4313.func_pm_ctrl_invert_if__mlxcx5jt", "facebookresearch__fvcore.a491d5b9.lm_rewrite__rzcypolg", "joke2k__faker-105", "python-hyper__h11.bed0dd4a.lm_rewrite__u43ah4d0", "jsvine__pdfplumber.02ff4313.lm_rewrite__fw6wobjn", "ipython__ipython-490", "pdfminer__pdfminer.six.1a8bd2f7.func_pm_remove_assign__jeg94vb0", "django-extensions__django-extensions-1654", "python-hyper__h11.bed0dd4a.lm_rewrite__pj91lelc", "ManimCommunity__manim-1053", "scoutapp__scout_apm_python-419", "python-poetry__poetry-1954", "alltheplaces__alltheplaces-3951", "facebookresearch__fairscale-221", "certbot__certbot-7101", "pyupio__safety.7654596b.lm_rewrite__pp28o0j3", "ARMmbed__greentea-243", "pydicom__pydicom.7d361b3d.lm_rewrite__v1d2z2kk", "dask__dask.5f61e423.lm_rewrite__gcvvgtp8", "encode__starlette.db5063c2.lm_rewrite__cwn42yjh", "mdn__kuma-6739", "DataDog__dd-trace-py-887", "conan-io__conan-center-index-4590", "django-money__django-money-594", "pulp__pulpcore-2635", "gweis__isodate.17cb25eb.lm_rewrite__vsq15jdh", "conan-io__conan.86f29e13.pr_15109", "gweis__isodate.17cb25eb.func_basic__tib6xdl1", "SAP__cloud-pysec-73", "wright-group__<PERSON><PERSON>ools-992", "pyca__pyopenssl.04766a49.lm_rewrite__ux62yoae", "falconry__falcon-174", "ibis-project__ibis-7472", "p<PERSON><PERSON><PERSON><PERSON>__satella-11", "oemof__oemof-solph-950", "jriddy__zerial-36", "saleor__saleor-5443", "Qiskit__qiskit-2255", "rasterio__rasterio-1991", "conda__conda-build-1544", "liqd__a4-opin-614", "streamlit__streamlit-8519", "Qiskit__qiskit-3159", "jazzband__django-axes-681", "dagster-io__dagster-13080", "electricitymaps__electricitymaps-contrib-1506", "interlegis__sapl-2044", "EdinburghGenomics__EGCG-Core-52", "freedomofpress__securedrop-4319", "django-cms__django-filer-970", "docker__docker-py-1901", "CTFd__CTFd-1876", "Project-MONAI__MONAI.a09c1f08.lm_rewrite__o3pjb3l0", "PennyLaneAI__pennylane-2766", "lepture__mistune.bf54ef67.lm_rewrite__9wsu6ol6", "encode__starlette.db5063c2.lm_rewrite__bg9fq2o5", "josh<PERSON><PERSON><PERSON><PERSON><PERSON>__django-bird-71", "repobee__repobee-300", "marshmallow-code__marshmallow-299", "PrefectHQ__prefect-2814", "googlefonts__picosvg-240", "googleapis__google-cloud-python-4716", "googleapis__python-storage-483", "PrefectHQ__prefect-1048", "docker__compose-40", "model-bakers__model_bakery-353", "spack__spack-19391", "vogt4nick__dequindre-53", "liberapay__liberapay.com-236", "certbot__certbot-5471", "linkedin__shiv-94", "deshima-dev__decode-16", "openshift__openshift-ansible-3156", "juju__python-libjuju-375", "spack__spack-40541", "joke2k__faker.8b401a7d.lm_rewrite__vxzf8ayn", "enthought__chaco-540", "mahmoud__glom.fb3c4e76.lm_rewrite__wyhqs6sy", "bokeh__bokeh-2820", "pallets__markupsafe.620c06c9.combine_file__ccc15c5c", "stanfordnlp__string2string.c4a72f59.func_pm_op_swap__5t4t1fa3", "PrefectHQ__prefect-4801", "hpcaitech__ColossalAI-5096", "benoitc__gunicorn.bacbf8aa.func_pm_remove_cond__zcejjh01", "ultralytics__ultralytics-12774", "sopel-irc__sopel-611", "marshmallow-code__marshmallow.9716fc62.combine_file__w6t6b3ut", "geopandas__geopandas-2172", "ipython__ipython-9833", "spack__spack-9072", "mesonbuild__meson-8978", "pdm-project__pdm-2817", "docker__compose-1870", "alltheplaces__alltheplaces-2457", "internetarchive__openlibrary-4341", "mido__mido.a0158ff9.lm_rewrite__xk06tvij", "netbox-community__netbox-7928", "ibis-project__ibis-6672", "spack__spack-19628", "open-mmlab__mmdetection-7147", "pwndbg__pwndbg-130", "marshmallow-code__apispec.8b421526.lm_rewrite__uzqosbyj", "conan-io__conan-center-index-16242", "Lightning-AI__pytorch-lightning-3751", "cal-itp__benefits-921", "mido__mido.a0158ff9.func_pm_ctrl_invert_if__ld0qgls5", "getmoto__moto-3040", "Flexget__Flexget-3491", "tensorflow__models-718", "PyGith<PERSON>__PyGithub-1641", "maxfischer2781__asyncstdlib-12", "sql-machine-learning__elasticdl-1247", "napari__napari-589", "python-pillow__<PERSON>llow-7094", "ibis-project__ibis-8195", "lifeomic__phc-sdk-py-69", "HIPS__autograd.ac044f0d.lm_rewrite__a0lzmq19", "iterative__dvc-775", "python-pillow__<PERSON>llow-3785", "pymodbus-dev__pymodbus-1512", "pozyt<PERSON><PERSON>ie__webapp-health-monitor-12", "cisagov__manage.get.gov-1302", "ray-project__ray-8572", "openstates__openstates-scrapers-1340", "hpcaitech__ColossalAI-4248", "common-workflow-language__cwltool-710", "pantsbuild__pants-15015", "zopefoundation__zope.component-63", "pyjanitor-devs__pyjanitor-289", "digitalfabrik__integreat-cms-645", "nickstenning__honcho-174", "paramiko__paramiko.23f92003.lm_rewrite__javnm5lb", "open-mmlab__mmcv-1138", "PrefectHQ__prefect-2685", "Pylons__pyramid-2917", "DataDog__dd-trace-py-2887", "python-trio__trio.cfbbe2c1.lm_rewrite__auc2kn82", "encode__starlette.db5063c2.lm_rewrite__plw2mlvm", "paperless-ngx__paperless-ngx-1605", "pantsbuild__pants-20069", "zestedesavoir__zds-site-4454", "cool-RR__PySnooper.57472b46.lm_rewrite__gia2uvnj", "ansible-collections__community.general-2201", "oauthlib__oauthlib.1fd52536.lm_rewrite__sejiv4di", "joke2k__faker.8b401a7d.lm_rewrite__zk4d8s47", "pyupio__safety.7654596b.lm_rewrite__all6h4po", "cookiecutter__cookiecutter.b4451231.lm_rewrite__aqe98bzo", "Qiskit__qiskit-12055", "pre-commit__pre-commit-1224", "ibis-project__ibis-7219", "django-money__django-money.835c1ab8.lm_rewrite__il2e81o1", "auth0__auth0-python-500", "AlexsLemonade__refinebio-3385", "biolab__orange3-2610", "wearewhys__magnivore-10", "lutris__lutris-3930", "great-expectations__great_expectations-1937", "sql-machine-learning__elasticdl-373", "mozilla__bleach.73871d76.lm_rewrite__6c10rrw7", "netbox-community__netbox-15026", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__0ya3bz6x", "Flexget__Flexget-1942", "facebookresearch__nevergrad-83", "paramiko__paramiko.23f92003.lm_rewrite__j0c5j8a5", "rotki__rotki-171", "conda__conda-build-537", "python-trio__trio-744", "oppia__oppia-8108", "Flexget__Flexget-1590", "Parsl__parsl-1314", "Django<PERSON><PERSON><PERSON>__djangogirls-983", "oppia__oppia-10983", "python-trio__trio.cfbbe2c1.lm_rewrite__w71ozq2u", "huggingface__optimum-1865", "seperman__deepdiff.ed252022.lm_rewrite__amcwunwf", "holoviz__panel-956", "mesonbuild__meson-3498", "sloria__environs-315", "bokeh__bokeh-8048", "fossasia__open-event-server-6871", "PokemonGoF__PokemonGo-Bot-6160", "ansible-collections__community.general-2848", "pallets__click.fde47b4b.lm_rewrite__w0flpohe", "iterative__dvc-1004", "poliastro__poliastro-51", "searx__searx-2391", "ManimCommunity__manim-1363", "ansible__ansible-36882", "auth0__auth0-python-537", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__52qalajw", "cknd__stackprinter.219fcc52.combine_file__3mb4yuht", "popylar__popylar-25", "ansible-collections__community.general-2894", "django-oscar__django-oscar-3343", "elastic__apm-agent-python-724", "huggingface__trl-934", "python-pillow__Pillow-3537", "benoitc__gunicorn.bacbf8aa.lm_rewrite__l4tg5kfz", "paperless-ngx__paperless-ngx-57", "stanfordnlp__string2string.c4a72f59.func_pm_op_swap__5quut7df", "nonebot__nonebot2-1720", "iterative__dvc.1d6ea681.func_pm_class_rm_funcs__uze5q3oz", "dbt-labs__dbt-core-8811", "lincolnloop__python-qrcode.456b01d4.lm_rewrite__nei28b1s", "MaxGhenis__microdf-80", "quantumlib__Cirq-2456", "huggingface__text-generation-inference-579", "spack__spack-37245", "pylint-dev__astroid.b114f6b5.lm_rewrite__xwnask7t", "gitpython-developers__GitPython-1015", "chainer__chainer-2686", "apache__airflow-16383", "pymeasure__pymeasure-284", "ansible-collections__community.general-8185", "ansible-collections__amazon.aws-943", "zestedesavoir__zds-site-5761", "borgbackup__borg-3896", "bokeh__bokeh-2273", "cupy__cupy-805", "wireservice__agate-sql-23", "pydicom__pydicom.7d361b3d.lm_rewrite__w10r5es2", "freqtrade__freqtrade-2565", "pretix__pretix-2030", "conda__conda-6367", "Parsl__parsl-203", "python-jsonschema__jsonschema.93e0caa5.lm_rewrite__n03efems", "jupyterlab__jupyterlab-12772", "Project-MONAI__MONAI.a09c1f08.func_pm_remove_assign__90ni1fns", "ansible-collections__community.aws-454", "biolab__orange3-3614", "googleapis__python-cloud-core-100", "weecology__retriever-1584", "uccser__verto-214", "ipython__ipython-1294", "pantsbuild__pants-5068", "conan-io__conan-center-index-2298", "bokeh__bokeh-9068", "Lightning-AI__lightning-2055", "python-pillow__Pillow-3808", "django-cms__django-filer-1189", "lepture__mistune.bf54ef67.func_pm_remove_cond__bgz1ybhp", "Cog-Creators__Red-DiscordBot.33e0eac7.func_basic__nmuajq34", "spotify__luigi-368", "huggingface__transformers-3147", "ross__requests-futures-28", "CiviWiki__OpenCiviWiki-863", "pyupio__safety.7654596b.lm_rewrite__x57dimnx", "conan-io__conan-2704", "Cog-Creators__Red-DiscordBot.33e0eac7.func_basic__zyccmzkb", "pudo__dataset.5c2dc8d3.lm_rewrite__3su3yv0o", "dask__dask.5f61e423.lm_rewrite__ei4edmzw", "spack__spack-31477", "pyqtgraph__pyqtgraph-2305", "facebookresearch__fvcore.a491d5b9.lm_rewrite__z7zvo500", "zalando-stups__senza-301", "tensorly__tensorly-170", "tobgu__pyrsistent-107", "python-amazon-mws__python-amazon-mws-198", "PyCQA__flake8.cf1542ce.lm_rewrite__wimss4pz", "facelessuser__soupsieve.a8080d97.lm_rewrite__sw9p7j55", "dask__dask.5f61e423.lm_rewrite__5elhetca", "pallets__click.fde47b4b.lm_rewrite__qtvws3zz", "pulp__pulpcore-4190", "ansible__ansible-23966", "spack__spack-19662", "rotki__rotki-897", "iterative__dvc.1d6ea681.pr_4124", "getnikola__nikola.0f4c230e.lm_rewrite__p9u4pp1c", "mdn__kuma-7869", "PyCQA__flake8.cf1542ce.lm_rewrite__tdi0h139", "PrefectHQ__prefect-2310", "ansible__ansible-35271", "facebookresearch__ParlAI-2029", "gitpython-developers__GitPython-746", "ktbyers__netmiko-2935", "bookwyrm-social__bookwyrm-995", "django__channels.a144b4b8.func_pm_class_rm_funcs__ldrdaw5e", "pytest-dev__iniconfig.16793ead.lm_rewrite__evuaqetw", "astropenguin__morecopy-34", "laterpay__laterpay-client-python-94", "biolab__orange3-2518", "flask-admin__flask-admin-1262", "getsentry__sentry-11585", "django-cms__django-cms-2755", "huggingface__transformers-22990", "Mailu__Mailu-840", "lutris__lutris-2674", "scrapinghub__shub-53", "dynaconf__dynaconf-457", "Pylons__pyramid-1648", "bentoml__BentoML-3636", "pygments__pygments.27649ebb.lm_rewrite__b8hl9zky", "dagster-io__dagster-1115", "conan-io__conan-4719", "r<PERSON><PERSON>__alive-progress.35853799.lm_rewrite__jzz25vx4", "django-money__django-money.835c1ab8.lm_rewrite__zza1zhvx", "huggingface__trl-911", "pyqtgraph__pyqtgraph-1408", "twosixlabs__armory-461", "great-expectations__great_expectations-6650", "ofek__pypinfo-109", "python-trio__trio.cfbbe2c1.lm_rewrite__tklenyxq", "alltheplaces__alltheplaces-5515", "Mimino666__langdetect.a1598f1a.combine_file__z2or4a24", "cknd__stackprinter.219fcc52.lm_rewrite__3mnfhdq1", "mne-tools__mne-bids-1125", "cookiecutter__cookiecutter-588", "alephdata__servicelayer-60", "fedora-infra__bodhi-3444", "pyg-team__pytorch_geometric-7902", "PyCQA__flake8.cf1542ce.lm_rewrite__3ufigs4e", "mesonbuild__meson-3036", "easybuilders__easybuild-easyblocks-1464", "kivy__python-for-android-1603", "pyupio__safety.7654596b.combine_file__ar1yz5p5", "cowrie__cowrie-392", "pallets__click-388", "oauthlib__oauthlib.1fd52536.lm_rewrite__uql5tgba", "ibis-project__ibis-627", "getsentry__sentry-python-1672", "plotly__plotly.py-2015", "duckinator__emanate-185", "marshmallow-code__marshmallow.9716fc62.lm_rewrite__e4j9zqsg", "wger-project__wger-170", "getnikola__nikola-2188", "stanfordnlp__dspy.651a4c71.lm_rewrite__kz94b36r", "jd__tenacity.0d40e76f.combine_file__wig9ao0c", "m<PERSON><PERSON>a__overrides-43", "bokeh__bokeh-8544", "PrefectHQ__prefect-3917", "googleapis__google-cloud-python-3424", "vllm-project__vllm-5290", "pypa__auditwheel-568", "ansible__ansible-28449", "jawah__charset_normalizer.1fdd6463.lm_rewrite__q6dme1rt", "deis__deis-280", "qtile__qtile-1578", "getsentry__sentry-5339", "praw-dev__praw-1145", "mozilla__bugbug-214", "pyinstaller__pyinstaller-5239", "netbox-community__netbox-14614", "elastic__apm-agent-python-1021", "lutris__lutris-1251", "ped<PERSON><PERSON>er__aiostomp-17", "xonsh__xonsh-3144", "Yelp__py_zipkin-107", "pdfminer__pdfminer.six.1a8bd2f7.combine_file__cgrkaq6d", "PyCQA__flake8.cf1542ce.lm_rewrite__ivcadf6q", "pytorch__ignite-426", "freqtrade__freqtrade-2467", "PyCQA__flake8.cf1542ce.lm_rewrite__dknnrpee", "mindsdb__mindsdb-2007", "activeloopai__deeplake-1738", "airctic__icevision-796", "mars-project__mars-2116", "python-jsonschema__jsonschema.93e0caa5.lm_rewrite__voc0wohq", "conda__conda-build-4953", "DataDog__integrations-extras-1550", "kedro-org__kedro-2087", "HIPS__autograd.ac044f0d.lm_rewrite__qw2cqxaf", "huggingface__transformers-21834", "dbt-labs__dbt-core-4359", "ncclient__ncclient-404", "networkx__networkx-6558", "pre-commit__pre-commit-hooks-519", "facebookresearch__hydra.0f03eb60.lm_rewrite__fd507nww", "pallets__click.fde47b4b.lm_rewrite__ifoxswpj", "Qiskit__qiskit-6020", "freqtrade__freqtrade-133", "oauthlib__oauthlib.1fd52536.lm_rewrite__52uqxs9w", "sublimelsp__LSP-491", "certbot__certbot-7503", "spack__spack-12246", "nginxinc__crossplane-69", "HIPS__autograd.ac044f0d.lm_rewrite__t1rxdv09", "conan-io__conan-center-index-6622", "yt-project__unyt-466", "pylint-dev__astroid.b114f6b5.lm_rewrite__s7qjwcu7", "napari__napari-5759", "pydantic__pydantic-8262", "encode__starlette-557", "jazzband__django-debug-toolbar-1331", "sqlfluff__sqlfluff.50a1c4b6.func_basic__6clyconx", "netromdk__vermin-8", "getsentry__sentry-5094", "peterbe__premailer-138", "PyCQA__flake8.cf1542ce.lm_rewrite__vg5vcbhm", "cookiecutter__cookiecutter.b4451231.lm_rewrite__bx2fomvs", "getsentry__responses-444", "pydicom__pydicom.7d361b3d.lm_rewrite__wgqnk46k", "ray-project__ray-9429", "xonsh__xonsh-3155", "Qiskit__qiskit-5588", "Qiskit__qiskit-4840", "aws__aws-cli-429", "Qiskit__qiskit-8426", "crytic__slither-617", "lepture__mistune.bf54ef67.lm_rewrite__cla62ryp", "GoogleCloudPlatform__professional-services-458", "dlr-eoc__ukis-pysat-91", "Qiskit__qiskit-4494", "django-cms__django-filer-1126", "MongoEngine__mongoengine-2858", "matthewwithanm__python-markdownify.6258f5c3.lm_rewrite__bdfqgtrq", "CrossGL__crosstl-213", "desihub__desiutil-93", "lincolnloop__python-qrcode.456b01d4.lm_rewrite__yo1tdmln", "docker__docker-py-1528", "freedomofpress__securedrop-2869", "bridge<PERSON><PERSON><PERSON>__checkov-489", "svthalia__concrexit-1793", "facebookresearch__fvcore.a491d5b9.lm_rewrite__apko0t48", "cekit__cekit-429", "conda__conda-7241", "obspy__obspy-2763", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__g9ut0thl", "django-money__django-money.835c1ab8.lm_rewrite__te7or9s5", "zopefoundation__RestrictedPython-288", "missionpinball__mpf-1331", "aws-cloudformation__cfn-lint-324", "conan-io__conan-8685", "Lightning-AI__pytorch-lightning-3042", "jsvine__pdfplumber.02ff4313.lm_rewrite__lbg2j43h", "jsch<PERSON>ier__django-storages-352", "modin-project__modin-591", "ourownstory__neural_prophet-1282", "Cloud-CV__EvalAI-1042", "iterative__dvc.1d6ea681.lm_rewrite__vro7l9m0", "mdowds__python-mock-firestore-32", "planetlabs__planet-client-python-816", "dask__dask.5f61e423.lm_rewrite__xp9ecoi3", "psf__black-2904", "DCC-Lab__RayTracing-220", "pseudonym117__Riot-Watcher-177", "facebookresearch__hydra.0f03eb60.lm_rewrite__7771z8ym", "graphql-python__graphene.82903263.combine_file__7qlr86wd", "wemake-services__wemake-python-styleguide-2529", "ultrabug__py3status-1549", "kedro-org__kedro-1633", "vyperlang__vyper-2176", "holoviz__panel-4441", "spack__spack-35368", "audeering__audiofile-117", "pylint-dev__astroid.b114f6b5.pr_2126", "pypa__pip-1745", "pandas-dev__pandas-16523", "PyCQA__flake8.cf1542ce.lm_rewrite__rx130wt2", "stanfordnlp__dspy.651a4c71.lm_rewrite__rffu0etx", "conan-io__conan-2870", "jupyterhub__jupyterhub-2062", "aws-cloudformation__cfn-lint-1647", "marshmallow-code__apispec.8b421526.lm_rewrite__0qho0dmb", "iterative__dvc-8381", "cantools__cantools.0c6a7871.lm_rewrite__dj96t3df", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__wg2os0dh", "hpcaitech__ColossalAI-3166", "piskvorky__gensim-1945", "robotframework__SeleniumLibrary-1431", "arviz-devs__arviz-619", "lepture__mistune.bf54ef67.lm_rewrite__r9aid04d", "jupyter__nbgrader-1410", "PrefectHQ__prefect-7336", "fossasia__open-event-server-5852", "google__turbinia-826", "django-import-export__django-import-export-1735", "django-json-api__django-rest-framework-json-api-630", "XKNX__xknx-1190", "digitalfabrik__integreat-cms-632", "realpython__codetiming-30", "elastic__apm-agent-python-1558", "Textualize__textual-3678", "benoitc__gunicorn.bacbf8aa.lm_rewrite__qx97z56z", "docker__compose-2617", "ivy-llc__ivy-19366", "marshmallow-code__webargs.dbde72fe.lm_rewrite__v0tsifwb", "MongoEngine__mongoengine-879", "r<PERSON><PERSON>__alive-progress.35853799.lm_rewrite__hgzxw1va", "joke2k__faker-1607", "pypa__virtualenv-2126", "rucio__rucio-3427", "typesafehub__conductr-cli-538", "stanfordnlp__string2string.c4a72f59.func_pm_op_break_chains__9bve09wo", "PrefectHQ__prefect-348", "DARMA-tasking__LB-analysis-framework-235", "encode__starlette-563", "scrapy__scrapy-5253", "picovico__python-sdk-13", "freqtrade__freqtrade-8273", "OpenMined__PySyft-3672", "lepture__mistune.bf54ef67.func_pm_ctrl_invert_if__vf7mq7hv", "kornia__kornia-2526", "sarugaku__plette-11", "pyqtgraph__pyqtgraph-458", "HIPS__autograd.ac044f0d.lm_rewrite__cetfos0u", "thp__urlwatch-726", "adrienverge__yamllint.8513d9b9.lm_rewrite__agc7y7gv", "spack__spack-36398", "pytorch__examples-182", "jsvine__pdfplumber.02ff4313.lm_rewrite__1puz01ea", "hydroshare__hydroshare-4819", "facebookresearch__habitat-lab-472", "modin-project__modin-3074", "marshmallow-code__apispec.8b421526.lm_rewrite__3yeskox9", "<PERSON><PERSON>AI__mycroft-core-2931", "netbox-community__netbox-4978", "mitmproxy__mitmproxy-2490", "Qiskit__qiskit-1613", "gruns__icecream.f76fef56.lm_rewrite__t4ny5zfz", "pallets__click.fde47b4b.lm_rewrite__xvtk49bf", "lepture__mistune.bf54ef67.lm_rewrite__vxz32tmf", "pantsbuild__pants-17637", "ipython__ipython-12889", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__m2vux0vj", "django-money__django-money.835c1ab8.lm_rewrite__aoih6jvn", "conan-io__conan-center-index-9172", "frispete__keyrings.cryptfile-31", "django__channels.a144b4b8.func_pm_ctrl_invert_if__duxuc5zq", "networkx__networkx-4831", "mesonbuild__meson-12233", "scikit-hep__hist-134", "facebookresearch__fairscale-1108", "cantools__cantools.0c6a7871.lm_rewrite__crhh68j4", "microsoft__AzureTRE-1686", "deshima-dev__deshima-sensitivity-96", "fidals__shopelectro-877", "pydicom__pydicom.7d361b3d.lm_rewrite__3gd7dmex", "fossasia__open-event-server-3131", "NVIDIA__NVFlare-314", "huggingface__transformers-15913", "codingedward__flask-sieve-31", "python-trio__trio.cfbbe2c1.lm_rewrite__2har2adr", "wearewhys__magnivore-12", "spack__spack-40298", "piskvorky__gensim-1887", "piskvorky__gensim-2147", "CiviWiki__OpenCiviWiki-622", "modin-project__modin-1072", "fjosw__pyerrors-45", "scrapy__w3lib-127", "corteva__geocube-166", "alltheplaces__alltheplaces-4224", "da<PERSON><PERSON><PERSON>__parso.338a5760.lm_rewrite__nqt2pd18", "streamlit__streamlit-6321", "gawel__pyquery.811cd048.lm_rewrite__o1v2xcun", "sql-machine-learning__elasticdl-1401", "nipy__nipype-3417", "scoutapp__scout_apm_python-494", "scanny__python-pptx.278b47b1.lm_rewrite__mfdrk0jx", "deepset-ai__haystack-6717", "stanfordnlp__string2string.c4a72f59.func_pm_op_swap__h58dkipe", "cool-RR__PySnooper.57472b46.lm_rewrite__urhyaplv", "fossasia__open-event-server-5226", "python-trio__trio.cfbbe2c1.lm_rewrite__dv4lz0f1", "paperless-ngx__paperless-ngx-903", "pre-commit__pre-commit-206", "modin-project__modin-1159", "modin-project__modin.8c7799fd.lm_rewrite__sg8bf6z1", "pydantic__pydantic.acb0f10f.lm_rewrite__thkgb2xw", "scverse__scanpy-1691", "mampfes__hacs_waste_collection_schedule-596", "huggingface__transformers-22776", "pdfminer__pdfminer.six.1a8bd2f7.lm_rewrite__tws7nl2y", "mkdocs__mkdocs-2478", "and<PERSON><PERSON>__sqlparse.e57923b3.func_basic__n7ehhdmu", "vega__altair-692", "spack__spack-36358", "cool-RR__PySnooper.57472b46.lm_rewrite__auwmcjys", "conan-io__conan-center-index-7037", "modin-project__modin-2173", "jupyter__nbgrader-954", "networkx__networkx-7477", "nose-devs__nose2-268", "PyGithub__PyGithub-743", "dbt-labs__dbt-core-5891", "tweepy__tweepy.91a41c6e.func_pm_remove_assign__b9nkory2", "jupyter__nbgrader-846", "docker__compose-2239", "mlflow__mlflow-6237", "hpcaitech__ColossalAI-1437", "fsspec__filesystem_spec-996", "vllm-project__vllm-153", "pydicom__pydicom.7d361b3d.combine_file__s4mhm2io", "scanny__python-pptx.278b47b1.lm_rewrite__0k49naq8", "carpentries__amy-2333", "kserve__kserve-889", "Erotemic__ubelt-64", "conan-io__conan.86f29e13.func_pm_remove_assign__05lzni20", "spack__spack-39660", "quantumlib__Cirq-4446", "spack__spack-5948", "benoitc__gunicorn.bacbf8aa.lm_rewrite__8myi9flw", "pyca__cryptography-8260", "Djan<PERSON><PERSON><PERSON><PERSON>__djangogirls-926", "open-telemetry__opentelemetry-python-contrib-1870", "agronholm__typeguard.b6a7e438.lm_rewrite__cz6032c7", "pyload__pyload-1375", "litestar-org__litestar-3478", "zulip__zulip-1065", "liqd__a4-me<PERSON><PERSON>lin-2372", "pypa__setuptools-1847", "HIPS__autograd.ac044f0d.lm_rewrite__g4kzefby", "Cog-Creators__Red-DiscordBot-1632", "car<PERSON><PERSON><PERSON>__django-filter-1614", "ocadotechnology__aimmo-60", "stanfordnlp__string2string.c4a72f59.func_pm_op_change_const__pjluaxq1", "getsentry__sentry-python-2762", "conan-io__conan-13211", "sublimelsp__LSP-1110", "BlueBrain__NeuroM-1001", "dmlc__dgl-946", "aws-cloudformation__cfn-lint-795", "cupy__cupy-7693", "pypa__setuptools-2583", "Project-MONAI__MONAI-2013", "pulp__pulpcore-3559", "dotkom__onlineweb4-1517", "python-trio__trio.cfbbe2c1.lm_rewrite__i8em647o", "voxel51__fiftyone-3322", "svthalia__concrexit-2711", "and<PERSON><PERSON>__sqlparse.e57923b3.lm_rewrite__x30cd7e6", "scrapy__w3lib-100", "digitalfabrik__integreat-cms-553", "agronholm__typeguard.b6a7e438.lm_rewrite__mgngjn90", "asottile__pyupgrade-767", "nautobot__nautobot-5223", "googleapis__python-bigquery-393", "pyupio__safety.7654596b.lm_rewrite__3vsihfg3", "pyg-team__pytorch_geometric-6546", "fossasia__open-event-server-4108", "spulec__freezegun.5f171db0.pr_527", "rsagroup__rsatoolbox-350", "docker__compose-4591", "dotkom__onlineweb4-578", "lnbits__lnbits-1183", "Lightning-Universe__lightning-flash-1667", "spack__spack-2523", "pydicom__pydicom.7d361b3d.lm_rewrite__3i3m6f54", "openstates__openstates-scrapers-2455"]}